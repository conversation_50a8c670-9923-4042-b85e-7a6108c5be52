AIProviderManager: Active provider: gemini_15
TranslationService: Using AI Provider Manager with active provider: gemini_15
AIProviderManager: Using primary provider: gemini_15
GeminiTranslationService (1.5): Using name dictionary with 5 entries
GeminiTranslationService (1.5): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (1.5): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (1.5): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (1.5): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (1.5): Name mapping - お兄さん → Onii-san (using translation)
Gemini15TranslationService: Attempt 1 of 3
Gemini15TranslationService: Using maxOutputTokens: 4096
Gemini15TranslationService: Non-retryable error, stopping retries
TranslationService: Provider failed, trying legacy Gemini fallback: HTTP error: 400
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Using maxOutputTokens: 8192
TranslationService: Gemini finishReason: STOP
TranslationService: API indicated completion with finish reason: STOP, skipping heuristic checks
TranslationService: Gemini language validation failed: Expected English translation but detected: unknown
TranslationService: Using AI Provider Manager with active provider: gemini_15
AIProviderManager: Using primary provider: gemini_15
GeminiTranslationService (1.5): Using name dictionary with 5 entries
GeminiTranslationService (1.5): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (1.5): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (1.5): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (1.5): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (1.5): Name mapping - お兄さん → Onii-san (using translation)
Gemini15TranslationService: Attempt 1 of 3
Gemini15TranslationService: Using maxOutputTokens: 4096
Gemini15TranslationService: Non-retryable error, stopping retries
TranslationService: Provider failed, trying legacy Gemini fallback: HTTP error: 400
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Using maxOutputTokens: 8192
TranslationService: Gemini finishReason: STOP
TranslationService: API indicated completion with finish reason: STOP, skipping heuristic checks
TranslationService: Gemini language validation failed: Expected English translation but detected: unknown
TranslationService: Using AI Provider Manager with active provider: gemini_15
AIProviderManager: Using primary provider: gemini_15
GeminiTranslationService (1.5): Using name dictionary with 5 entries
GeminiTranslationService (1.5): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (1.5): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (1.5): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (1.5): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (1.5): Name mapping - お兄さん → Onii-san (using translation)
Gemini15TranslationService: Attempt 1 of 3
Gemini15TranslationService: Using maxOutputTokens: 4096
Gemini15TranslationService: Non-retryable error, stopping retries
TranslationService: Provider failed, trying legacy Gemini fallback: HTTP error: 400
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Using maxOutputTokens: 8192
TranslationService: Gemini finishReason: STOP
TranslationService: API indicated completion with finish reason: STOP, skipping heuristic checks
TranslationService: Gemini language validation passed - detected english
TranslationService: Gemini dialogue tag issues detected: All dialogue quotation marks were lost in translation
TranslationService: Applied Gemini dialogue tag cleanup
TranslationService: Applying Gemini name substitutions with 5 dictionary entries
TranslationService: No Gemini name substitutions applied
TranslationService: Using AI Provider Manager with active provider: gemini_15
AIProviderManager: Using primary provider: gemini_15
GeminiTranslationService (1.5): Using name dictionary with 5 entries
GeminiTranslationService (1.5): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (1.5): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (1.5): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (1.5): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (1.5): Name mapping - お兄さん → Onii-san (using translation)
Gemini15TranslationService: Attempt 1 of 3
Gemini15TranslationService: Using maxOutputTokens: 4096
Gemini15TranslationService: Non-retryable error, stopping retries
TranslationService: Provider failed, trying legacy Gemini fallback: HTTP error: 400
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Using maxOutputTokens: 8192
TranslationService: Gemini finishReason: STOP
TranslationService: API indicated completion with finish reason: STOP, skipping heuristic checks
TranslationService: Gemini language validation failed: Expected English translation but detected: unknown
TranslationService: Using AI Provider Manager with active provider: gemini_15
AIProviderManager: Using primary provider: gemini_15
GeminiTranslationService (1.5): Using name dictionary with 5 entries
GeminiTranslationService (1.5): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (1.5): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (1.5): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (1.5): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (1.5): Name mapping - お兄さん → Onii-san (using translation)
Gemini15TranslationService: Attempt 1 of 3
Gemini15TranslationService: Using maxOutputTokens: 4096
Gemini15TranslationService: Non-retryable error, stopping retries
TranslationService: Provider failed, trying legacy Gemini fallback: HTTP error: 400
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Using maxOutputTokens: 8192
TranslationService: Gemini finishReason: STOP
TranslationService: API indicated completion with finish reason: STOP, skipping heuristic checks
TranslationService: Gemini language validation passed - detected english
TranslationService: Gemini dialogue tag issues detected: All dialogue quotation marks were lost in translation
TranslationService: Applied Gemini dialogue tag cleanup
TranslationService: Applying Gemini name substitutions with 5 dictionary entries
TranslationService: No Gemini name substitutions applied
TranslationService: Using AI Provider Manager with active provider: gemini_15
AIProviderManager: Using primary provider: gemini_15
GeminiTranslationService (1.5): Using name dictionary with 5 entries
GeminiTranslationService (1.5): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (1.5): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (1.5): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (1.5): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (1.5): Name mapping - お兄さん → Onii-san (using translation)
Gemini15TranslationService: Attempt 1 of 3
Gemini15TranslationService: Using maxOutputTokens: 4096
Gemini15TranslationService: Non-retryable error, stopping retries
TranslationService: Provider failed, trying legacy Gemini fallback: HTTP error: 400
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Using maxOutputTokens: 8192
TranslationService: Gemini finishReason: STOP
TranslationService: API indicated completion with finish reason: STOP, skipping heuristic checks
TranslationService: Gemini language validation passed - detected english
TranslationService: Gemini dialogue tag issues detected: Significant change in dialogue quotation mark count: 614 → 39
TranslationService: Applied Gemini dialogue tag cleanup
TranslationService: Applying Gemini name substitutions with 5 dictionary entries
TranslationService: No Gemini name substitutions applied
AIProviderManager: Active provider: gemini_15
AIProviderManager: Active provider changed to: gemini_15
GeminiTranslationService (1.5): Using name dictionary with 5 entries
GeminiTranslationService (1.5): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (1.5): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (1.5): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (1.5): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (1.5): Name mapping - お兄さん → Onii-san (using translation)
Gemini15TranslationService: Attempt 1 of 3
Gemini15TranslationService: Using maxOutputTokens: 4096
AIProviderManager: Active provider changed to: gemini_20
GeminiTranslationService (2.0): Using name dictionary with 5 entries
GeminiTranslationService (2.0): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (2.0): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (2.0): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お兄さん → Onii-san (using translation)
Gemini20TranslationService: Attempt 1 of 3
Gemini20TranslationService: Using maxOutputTokens: 5120
AIProviderManager: Active provider changed to: gemini_15
GeminiTranslationService (1.5): Using name dictionary with 5 entries
GeminiTranslationService (1.5): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (1.5): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (1.5): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (1.5): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (1.5): Name mapping - お兄さん → Onii-san (using translation)
Gemini15TranslationService: Attempt 1 of 3
Gemini15TranslationService: Using maxOutputTokens: 4096
AIProviderManager: Active provider changed to: gemini_20
GeminiTranslationService (2.0): Using name dictionary with 5 entries
GeminiTranslationService (2.0): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (2.0): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (2.0): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お兄さん → Onii-san (using translation)
Gemini20TranslationService: Attempt 1 of 3
Gemini20TranslationService: Using maxOutputTokens: 5120
AIProviderManager: Active provider changed to: gemini_15
GeminiTranslationService (1.5): Using name dictionary with 5 entries
GeminiTranslationService (1.5): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (1.5): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (1.5): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (1.5): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (1.5): Name mapping - お兄さん → Onii-san (using translation)
Gemini15TranslationService: Attempt 1 of 3
Gemini15TranslationService: Using maxOutputTokens: 4096
Gemini15TranslationService: Non-retryable error, stopping retries
AIProviderManager: Active provider changed to: gemini_20
GeminiTranslationService (2.0): Using name dictionary with 5 entries
GeminiTranslationService (2.0): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (2.0): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (2.0): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お兄さん → Onii-san (using translation)
Gemini20TranslationService: Attempt 1 of 3
Gemini20TranslationService: Using maxOutputTokens: 5120
Gemini20TranslationService: Non-retryable error, stopping retries
AIProviderManager: Active provider: gemini_20
TranslationService: Using AI Provider Manager with active provider: gemini_20
AIProviderManager: Using primary provider: gemini_20
GeminiTranslationService (2.0): Using name dictionary with 182 entries
GeminiTranslationService (2.0): Name mapping - 父さん → Tou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (2.0): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 母さん → Kaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お姉ちゃん → Onee-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お兄さん → Onii-san (using translation)
GeminiTranslationService (2.0): Name mapping - 剣聖 → Sword Saint (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力砲 → magic cannon (using translation)
GeminiTranslationService (2.0): Name mapping - 影 → Shadow (using translation)
GeminiTranslationService (2.0): Name mapping - タイタンズ → Titans (using translation)
GeminiTranslationService (2.0): Name mapping - 魔道具 → Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラゴン → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 回復アイテム → Healing Item (using translation)
GeminiTranslationService (2.0): Name mapping - アイン → Ain (using translation)
GeminiTranslationService (2.0): Name mapping - ライク → Laik (using translation)
GeminiTranslationService (2.0): Name mapping - 氷狼一閃 → Hyoro Issen (Ice Wolf Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流 → Majin Ryu (Demon God Style) (using translation)
GeminiTranslationService (2.0): Name mapping - 剣術 → Kenjutsu (Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法スキル → Magic Skill (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣 → Magic Beast (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流剣術 → Majin Ryu Kenjutsu (Demon God Style Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜炎斬 → Kokuryu Enzan (Black Dragon Flame Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョン → Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣災害 → Magic Beast Disaster (using translation)
GeminiTranslationService (2.0): Name mapping - 炎のブレス → Flame Breath (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼……一閃》 → 《Hyoro…Issen (Ice Wolf…Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼一閃》 → 《Hyoro Issen (Ice Wolf Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《黒竜炎斬》 → 《Kokuryu Enzan (Black Dragon Flame Slash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 紅蓮の炎 → Crimson Flame (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ → Tilty (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド → Soldo (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア → Shirlia (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家 → Marquis Household (using translation)
GeminiTranslationService (2.0): Name mapping - 《千里眼》 → Senrigan (Clairvoyance) (using translation)
GeminiTranslationService (2.0): Name mapping - ドラゴン → Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法 → Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力 → Magical Power (using translation)
GeminiTranslationService (2.0): Name mapping - 中立的な家臣達 → Neutral Retainers (using translation)
GeminiTranslationService (2.0): Name mapping - 対毒魔法 → Anti-Poison Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 妹達 → Imoto-tachi (using translation)
GeminiTranslationService (2.0): Name mapping - 次期当主 → Next Family Head (using translation)
GeminiTranslationService (2.0): Name mapping - シルリアさん → Shirlia-san (using translation)
GeminiTranslationService (2.0): Name mapping - 睡眠薬 → Sleeping pill (using translation)
GeminiTranslationService (2.0): Name mapping - ポヤポヤ → Fuzzy / Dazed / Absent-minded (using translation)
GeminiTranslationService (2.0): Name mapping - ホッ → Relief (sigh of relief) (using translation)
GeminiTranslationService (2.0): Name mapping - ゾッコン → Deeply in love / Infatuated (using translation)
GeminiTranslationService (2.0): Name mapping - メイド → Maid (using translation)
GeminiTranslationService (2.0): Name mapping - 心底ホッ → Deep Relief (using translation)
GeminiTranslationService (2.0): Name mapping - 腹黒メガネ → Scheming Glasses-wearer (using translation)
GeminiTranslationService (2.0): Name mapping - アインさん → Ain-san (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラ → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - アイテム → Item (using translation)
GeminiTranslationService (2.0): Name mapping - ダメージ → Damage (using translation)
GeminiTranslationService (2.0): Name mapping - リカバリー → Recovery (using translation)
GeminiTranslationService (2.0): Name mapping - プレイヤー → Player (using translation)
GeminiTranslationService (2.0): Name mapping - 炎球 → Fireball (using translation)
GeminiTranslationService (2.0): Name mapping - リスク → Risk (using translation)
GeminiTranslationService (2.0): Name mapping - ラゴン → ragon (using translation)
GeminiTranslationService (2.0): Name mapping - ライク様 → Laik-sama (using translation)
GeminiTranslationService (2.0): Name mapping - レンジャー領 → Ranger Territory (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士 → Knight (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商人 → Slave Trader (using translation)
GeminiTranslationService (2.0): Name mapping - ガストの町 → Gast Town (using translation)
GeminiTranslationService (2.0): Name mapping - 医務室 → Infirmary (using translation)
GeminiTranslationService (2.0): Name mapping - 支配の呪具 → Dominance Curse Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士団 → Knight Order (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵令嬢 → marquis’ daughter (using translation)
GeminiTranslationService (2.0): Name mapping - 闇巨人 → Dark Giant (using translation)
GeminiTranslationService (2.0): Name mapping - ガランド・レンジャー → Garand Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 王都 → Royal Capital (using translation)
GeminiTranslationService (2.0): Name mapping - 門 → Gate (using translation)
GeminiTranslationService (2.0): Name mapping - 二の型 → Second Form (using translation)
GeminiTranslationService (2.0): Name mapping - ライク・ハルトマン → Laik Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - 三の型 → Third Form (using translation)
GeminiTranslationService (2.0): Name mapping - ミラウ → Mirau (using translation)
GeminiTranslationService (2.0): Name mapping - フレイ → Frey (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫空閃 → Tenki Kusen (Celestial Princess Sky Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - 空間転移 → Spatial Transfer (using translation)
GeminiTranslationService (2.0): Name mapping - 剣士 → Swordsman (using translation)
GeminiTranslationService (2.0): Name mapping - 運び屋 → Carrier (using translation)
GeminiTranslationService (2.0): Name mapping - 訓練場 → Training Grounds (using translation)
GeminiTranslationService (2.0): Name mapping - パパ → Dad (using translation)
GeminiTranslationService (2.0): Name mapping - 氷華 → Ice Flower (using translation)
GeminiTranslationService (2.0): Name mapping - 師匠 → shisho (using translation)
GeminiTranslationService (2.0): Name mapping - 飛行 → Flight (using translation)
GeminiTranslationService (2.0): Name mapping - 風刃 → Wind Blade (using translation)
GeminiTranslationService (2.0): Name mapping - 好感度 → favorability (using translation)
GeminiTranslationService (2.0): Name mapping - 二刀流 → Dual-wielding (using translation)
GeminiTranslationService (2.0): Name mapping - 四の型 → Fourth Form (using translation)
GeminiTranslationService (2.0): Name mapping - 氷炎万華 → Hyoen Mange (Ice Flame Myriad Flowers) (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵家 → Baron's family (using translation)
GeminiTranslationService (2.0): Name mapping - 公爵家 → Duke's family (using translation)
GeminiTranslationService (2.0): Name mapping - 執事 → Butler (using translation)
GeminiTranslationService (2.0): Name mapping - テラード・マジェスター → Terrard Majester (using translation)
GeminiTranslationService (2.0): Name mapping - 闇組織 → Dark Organization (using translation)
GeminiTranslationService (2.0): Name mapping - 世界征服 → World Domination (using translation)
GeminiTranslationService (2.0): Name mapping - 従者 → Retainer (using translation)
GeminiTranslationService (2.0): Name mapping - 爵位 → Peerage/Title of Nobility (using translation)
GeminiTranslationService (2.0): Name mapping - 家格 → Rank (using translation)
GeminiTranslationService (2.0): Name mapping - 大道芸人 → Street Performer (using translation)
GeminiTranslationService (2.0): Name mapping - 王家 → Royal Family (using translation)
GeminiTranslationService (2.0): Name mapping - 学園 → Academy (using translation)
GeminiTranslationService (2.0): Name mapping - 魔石 → Magic Stone (using translation)
GeminiTranslationService (2.0): Name mapping - クラーク・ハルトマン → Clark Hartman (using translation)
GeminiTranslationService (2.0): Name mapping - 伯爵位 → Earldom (using translation)
GeminiTranslationService (2.0): Name mapping - 準貴族 → Quasi-Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 木っ端貴族 → Minor Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 刀 → Katana (using translation)
GeminiTranslationService (2.0): Name mapping - 応接室 → Reception Room (using translation)
GeminiTranslationService (2.0): Name mapping - 芸者 → Geisha (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト村 → Luruto Village (using translation)
GeminiTranslationService (2.0): Name mapping - 村長 → Village Chief (using translation)
GeminiTranslationService (2.0): Name mapping - コルス → Kols (using translation)
GeminiTranslationService (2.0): Name mapping - スフィア → Sufia (using translation)
GeminiTranslationService (2.0): Name mapping - ノブファンプレイヤー → Nobu Fan Player (using translation)
GeminiTranslationService (2.0): Name mapping - 聖属性 → Holy Attribute (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法学園 → Magic Academy (using translation)
GeminiTranslationService (2.0): Name mapping - クヴァト → Kuvato (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョンボス → Dungeon Boss (using translation)
GeminiTranslationService (2.0): Name mapping - 一の型 → First Form (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョ → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 獣タイプ → Beast Type (using translation)
GeminiTranslationService (2.0): Name mapping - 幽霊 → Ghost (using translation)
GeminiTranslationService (2.0): Name mapping - 剣舞 → Sword Dance (using translation)
GeminiTranslationService (2.0): Name mapping - 幽剣乱舞 → Yūken Ranbu (Phantom Sword Dance) (using translation)
GeminiTranslationService (2.0): Name mapping - ジャグリング → Juggling (using translation)
GeminiTranslationService (2.0): Name mapping - 村人 → Villager (using translation)
GeminiTranslationService (2.0): Name mapping - 五の型 → Fifth Form (using translation)
GeminiTranslationService (2.0): Name mapping - クロウ・デア・アルバート → Crow Der Albert (using translation)
GeminiTranslationService (2.0): Name mapping - アルバート王国 → Albert Kingdom (using translation)
GeminiTranslationService (2.0): Name mapping - 第一王子 → First Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 王家の“影” → Royal "Shadows" (using translation)
GeminiTranslationService (2.0): Name mapping - 製法 → Manufacturing Method (using translation)
GeminiTranslationService (2.0): Name mapping - ルイス → Louis (using translation)
GeminiTranslationService (2.0): Name mapping - 映写魔道具 → Projection Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 辺境の村 → Frontier Village (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト → Luruto (using translation)
GeminiTranslationService (2.0): Name mapping - 地図 → Map (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョン → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 木剣 → Wooden Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワー → Charisma (using translation)
GeminiTranslationService (2.0): Name mapping - 空間ごと隔絶 → separated space (using translation)
GeminiTranslationService (2.0): Name mapping - 闇魔法 → Dark Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワ → Charisma Power (using translation)
GeminiTranslationService (2.0): Name mapping - コボルト → Kobold (using translation)
GeminiTranslationService (2.0): Name mapping - 王子殿下 → Your Royal Highness / Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 攻略対象 → Capture Target (using translation)
GeminiTranslationService (2.0): Name mapping - チートキャラ → Cheat Character (using translation)
GeminiTranslationService (2.0): Name mapping - ゴブリン → Goblin (using translation)
GeminiTranslationService (2.0): Name mapping - ホブゴブリン → Hobgoblin (using translation)
GeminiTranslationService (2.0): Name mapping - 棍棒 → Club (using translation)
GeminiTranslationService (2.0): Name mapping - 幻の剣 → Phantom Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 宮廷剣術 → Court Swordsmanship (using translation)
GeminiTranslationService (2.0): Name mapping - マジェスター公爵家 → Majester Ducal House (using translation)
GeminiTranslationService (2.0): Name mapping - オープニングイベント → Opening Event (using translation)
GeminiTranslationService (2.0): Name mapping - 炎の剣 → Sword of Flame (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵 → baron (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜 → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - お父様 → Otou-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お母様 → Okaa-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お姉さん → Onee-san (using translation)
GeminiTranslationService (2.0): Name mapping - おじさん → Ojisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばさん → Obasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいさん → Ojiisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあさん → Obaasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいちゃん → Ojiichan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあちゃん → Obaachan (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家の屋敷 → marquis&#039;s estate (using translation)
GeminiTranslationService (2.0): Name mapping - ドゴラ → Dogora (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商 → Slave Merchant (using translation)
GeminiTranslationService (2.0): Name mapping - 呪具 → Cursed Item (using translation)
GeminiTranslationService (2.0): Name mapping - 魔人 → Demon (using translation)
GeminiTranslationService (2.0): Name mapping - 闇天太陽 → Dark Sky Sun (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫、空閃 → Tenki, Kusen (Sky Princess, Sky Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - ワルガー → Walgar (using translation)
GeminiTranslationService (2.0): Name mapping - ハルトマン侯爵家 → Hartmann Marquisate Family (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア・ハルトマン → Shirlia Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ・レンジャー → Tilty Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド・レンジャー → Soldo Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 千里眼 → Clairvoyance (using translation)
Gemini20TranslationService: Attempt 1 of 3
Gemini20TranslationService: Using maxOutputTokens: 5120
Gemini20TranslationService: Non-retryable error, stopping retries
TranslationService: Provider failed, trying legacy Gemini fallback: HTTP error: 400
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Using maxOutputTokens: 8192
TranslationService: Gemini finishReason: STOP
TranslationService: API indicated completion with finish reason: STOP, skipping heuristic checks
TranslationService: Gemini language validation passed - detected english
TranslationService: Gemini dialogue tag issues detected: Added dialogue tags not present in original: he said, Some dialogue tags could not be automatically removed: he said, Significant change in dialogue quotation mark count: 163 → 5
TranslationService: Applied Gemini dialogue tag cleanup
TranslationService: Applying Gemini name substitutions with 182 dictionary entries
TranslationService: No Gemini name substitutions applied
TranslationService: Using AI Provider Manager with active provider: gemini_20
AIProviderManager: Using primary provider: gemini_20
GeminiTranslationService (2.0): Using name dictionary with 182 entries
GeminiTranslationService (2.0): Name mapping - 父さん → Tou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (2.0): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 母さん → Kaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お姉ちゃん → Onee-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お兄さん → Onii-san (using translation)
GeminiTranslationService (2.0): Name mapping - 剣聖 → Sword Saint (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力砲 → magic cannon (using translation)
GeminiTranslationService (2.0): Name mapping - 影 → Shadow (using translation)
GeminiTranslationService (2.0): Name mapping - タイタンズ → Titans (using translation)
GeminiTranslationService (2.0): Name mapping - 魔道具 → Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラゴン → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 回復アイテム → Healing Item (using translation)
GeminiTranslationService (2.0): Name mapping - アイン → Ain (using translation)
GeminiTranslationService (2.0): Name mapping - ライク → Laik (using translation)
GeminiTranslationService (2.0): Name mapping - 氷狼一閃 → Hyoro Issen (Ice Wolf Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流 → Majin Ryu (Demon God Style) (using translation)
GeminiTranslationService (2.0): Name mapping - 剣術 → Kenjutsu (Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法スキル → Magic Skill (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣 → Magic Beast (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流剣術 → Majin Ryu Kenjutsu (Demon God Style Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜炎斬 → Kokuryu Enzan (Black Dragon Flame Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョン → Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣災害 → Magic Beast Disaster (using translation)
GeminiTranslationService (2.0): Name mapping - 炎のブレス → Flame Breath (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼……一閃》 → 《Hyoro…Issen (Ice Wolf…Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼一閃》 → 《Hyoro Issen (Ice Wolf Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《黒竜炎斬》 → 《Kokuryu Enzan (Black Dragon Flame Slash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 紅蓮の炎 → Crimson Flame (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ → Tilty (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド → Soldo (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア → Shirlia (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家 → Marquis Household (using translation)
GeminiTranslationService (2.0): Name mapping - 《千里眼》 → Senrigan (Clairvoyance) (using translation)
GeminiTranslationService (2.0): Name mapping - ドラゴン → Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法 → Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力 → Magical Power (using translation)
GeminiTranslationService (2.0): Name mapping - 中立的な家臣達 → Neutral Retainers (using translation)
GeminiTranslationService (2.0): Name mapping - 対毒魔法 → Anti-Poison Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 妹達 → Imoto-tachi (using translation)
GeminiTranslationService (2.0): Name mapping - 次期当主 → Next Family Head (using translation)
GeminiTranslationService (2.0): Name mapping - シルリアさん → Shirlia-san (using translation)
GeminiTranslationService (2.0): Name mapping - 睡眠薬 → Sleeping pill (using translation)
GeminiTranslationService (2.0): Name mapping - ポヤポヤ → Fuzzy / Dazed / Absent-minded (using translation)
GeminiTranslationService (2.0): Name mapping - ホッ → Relief (sigh of relief) (using translation)
GeminiTranslationService (2.0): Name mapping - ゾッコン → Deeply in love / Infatuated (using translation)
GeminiTranslationService (2.0): Name mapping - メイド → Maid (using translation)
GeminiTranslationService (2.0): Name mapping - 心底ホッ → Deep Relief (using translation)
GeminiTranslationService (2.0): Name mapping - 腹黒メガネ → Scheming Glasses-wearer (using translation)
GeminiTranslationService (2.0): Name mapping - アインさん → Ain-san (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラ → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - アイテム → Item (using translation)
GeminiTranslationService (2.0): Name mapping - ダメージ → Damage (using translation)
GeminiTranslationService (2.0): Name mapping - リカバリー → Recovery (using translation)
GeminiTranslationService (2.0): Name mapping - プレイヤー → Player (using translation)
GeminiTranslationService (2.0): Name mapping - 炎球 → Fireball (using translation)
GeminiTranslationService (2.0): Name mapping - リスク → Risk (using translation)
GeminiTranslationService (2.0): Name mapping - ラゴン → ragon (using translation)
GeminiTranslationService (2.0): Name mapping - ライク様 → Laik-sama (using translation)
GeminiTranslationService (2.0): Name mapping - レンジャー領 → Ranger Territory (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士 → Knight (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商人 → Slave Trader (using translation)
GeminiTranslationService (2.0): Name mapping - ガストの町 → Gast Town (using translation)
GeminiTranslationService (2.0): Name mapping - 医務室 → Infirmary (using translation)
GeminiTranslationService (2.0): Name mapping - 支配の呪具 → Dominance Curse Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士団 → Knight Order (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵令嬢 → marquis’ daughter (using translation)
GeminiTranslationService (2.0): Name mapping - 闇巨人 → Dark Giant (using translation)
GeminiTranslationService (2.0): Name mapping - ガランド・レンジャー → Garand Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 王都 → Royal Capital (using translation)
GeminiTranslationService (2.0): Name mapping - 門 → Gate (using translation)
GeminiTranslationService (2.0): Name mapping - 二の型 → Second Form (using translation)
GeminiTranslationService (2.0): Name mapping - ライク・ハルトマン → Laik Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - 三の型 → Third Form (using translation)
GeminiTranslationService (2.0): Name mapping - ミラウ → Mirau (using translation)
GeminiTranslationService (2.0): Name mapping - フレイ → Frey (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫空閃 → Tenki Kusen (Celestial Princess Sky Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - 空間転移 → Spatial Transfer (using translation)
GeminiTranslationService (2.0): Name mapping - 剣士 → Swordsman (using translation)
GeminiTranslationService (2.0): Name mapping - 運び屋 → Carrier (using translation)
GeminiTranslationService (2.0): Name mapping - 訓練場 → Training Grounds (using translation)
GeminiTranslationService (2.0): Name mapping - パパ → Dad (using translation)
GeminiTranslationService (2.0): Name mapping - 氷華 → Ice Flower (using translation)
GeminiTranslationService (2.0): Name mapping - 師匠 → shisho (using translation)
GeminiTranslationService (2.0): Name mapping - 飛行 → Flight (using translation)
GeminiTranslationService (2.0): Name mapping - 風刃 → Wind Blade (using translation)
GeminiTranslationService (2.0): Name mapping - 好感度 → favorability (using translation)
GeminiTranslationService (2.0): Name mapping - 二刀流 → Dual-wielding (using translation)
GeminiTranslationService (2.0): Name mapping - 四の型 → Fourth Form (using translation)
GeminiTranslationService (2.0): Name mapping - 氷炎万華 → Hyoen Mange (Ice Flame Myriad Flowers) (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵家 → Baron's family (using translation)
GeminiTranslationService (2.0): Name mapping - 公爵家 → Duke's family (using translation)
GeminiTranslationService (2.0): Name mapping - 執事 → Butler (using translation)
GeminiTranslationService (2.0): Name mapping - テラード・マジェスター → Terrard Majester (using translation)
GeminiTranslationService (2.0): Name mapping - 闇組織 → Dark Organization (using translation)
GeminiTranslationService (2.0): Name mapping - 世界征服 → World Domination (using translation)
GeminiTranslationService (2.0): Name mapping - 従者 → Retainer (using translation)
GeminiTranslationService (2.0): Name mapping - 爵位 → Peerage/Title of Nobility (using translation)
GeminiTranslationService (2.0): Name mapping - 家格 → Rank (using translation)
GeminiTranslationService (2.0): Name mapping - 大道芸人 → Street Performer (using translation)
GeminiTranslationService (2.0): Name mapping - 王家 → Royal Family (using translation)
GeminiTranslationService (2.0): Name mapping - 学園 → Academy (using translation)
GeminiTranslationService (2.0): Name mapping - 魔石 → Magic Stone (using translation)
GeminiTranslationService (2.0): Name mapping - クラーク・ハルトマン → Clark Hartman (using translation)
GeminiTranslationService (2.0): Name mapping - 伯爵位 → Earldom (using translation)
GeminiTranslationService (2.0): Name mapping - 準貴族 → Quasi-Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 木っ端貴族 → Minor Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 刀 → Katana (using translation)
GeminiTranslationService (2.0): Name mapping - 応接室 → Reception Room (using translation)
GeminiTranslationService (2.0): Name mapping - 芸者 → Geisha (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト村 → Luruto Village (using translation)
GeminiTranslationService (2.0): Name mapping - 村長 → Village Chief (using translation)
GeminiTranslationService (2.0): Name mapping - コルス → Kols (using translation)
GeminiTranslationService (2.0): Name mapping - スフィア → Sufia (using translation)
GeminiTranslationService (2.0): Name mapping - ノブファンプレイヤー → Nobu Fan Player (using translation)
GeminiTranslationService (2.0): Name mapping - 聖属性 → Holy Attribute (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法学園 → Magic Academy (using translation)
GeminiTranslationService (2.0): Name mapping - クヴァト → Kuvato (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョンボス → Dungeon Boss (using translation)
GeminiTranslationService (2.0): Name mapping - 一の型 → First Form (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョ → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 獣タイプ → Beast Type (using translation)
GeminiTranslationService (2.0): Name mapping - 幽霊 → Ghost (using translation)
GeminiTranslationService (2.0): Name mapping - 剣舞 → Sword Dance (using translation)
GeminiTranslationService (2.0): Name mapping - 幽剣乱舞 → Yūken Ranbu (Phantom Sword Dance) (using translation)
GeminiTranslationService (2.0): Name mapping - ジャグリング → Juggling (using translation)
GeminiTranslationService (2.0): Name mapping - 村人 → Villager (using translation)
GeminiTranslationService (2.0): Name mapping - 五の型 → Fifth Form (using translation)
GeminiTranslationService (2.0): Name mapping - クロウ・デア・アルバート → Crow Der Albert (using translation)
GeminiTranslationService (2.0): Name mapping - アルバート王国 → Albert Kingdom (using translation)
GeminiTranslationService (2.0): Name mapping - 第一王子 → First Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 王家の“影” → Royal "Shadows" (using translation)
GeminiTranslationService (2.0): Name mapping - 製法 → Manufacturing Method (using translation)
GeminiTranslationService (2.0): Name mapping - ルイス → Louis (using translation)
GeminiTranslationService (2.0): Name mapping - 映写魔道具 → Projection Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 辺境の村 → Frontier Village (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト → Luruto (using translation)
GeminiTranslationService (2.0): Name mapping - 地図 → Map (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョン → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 木剣 → Wooden Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワー → Charisma (using translation)
GeminiTranslationService (2.0): Name mapping - 空間ごと隔絶 → separated space (using translation)
GeminiTranslationService (2.0): Name mapping - 闇魔法 → Dark Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワ → Charisma Power (using translation)
GeminiTranslationService (2.0): Name mapping - コボルト → Kobold (using translation)
GeminiTranslationService (2.0): Name mapping - 王子殿下 → Your Royal Highness / Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 攻略対象 → Capture Target (using translation)
GeminiTranslationService (2.0): Name mapping - チートキャラ → Cheat Character (using translation)
GeminiTranslationService (2.0): Name mapping - ゴブリン → Goblin (using translation)
GeminiTranslationService (2.0): Name mapping - ホブゴブリン → Hobgoblin (using translation)
GeminiTranslationService (2.0): Name mapping - 棍棒 → Club (using translation)
GeminiTranslationService (2.0): Name mapping - 幻の剣 → Phantom Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 宮廷剣術 → Court Swordsmanship (using translation)
GeminiTranslationService (2.0): Name mapping - マジェスター公爵家 → Majester Ducal House (using translation)
GeminiTranslationService (2.0): Name mapping - オープニングイベント → Opening Event (using translation)
GeminiTranslationService (2.0): Name mapping - 炎の剣 → Sword of Flame (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵 → baron (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜 → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - お父様 → Otou-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お母様 → Okaa-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お姉さん → Onee-san (using translation)
GeminiTranslationService (2.0): Name mapping - おじさん → Ojisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばさん → Obasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいさん → Ojiisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあさん → Obaasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいちゃん → Ojiichan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあちゃん → Obaachan (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家の屋敷 → marquis&#039;s estate (using translation)
GeminiTranslationService (2.0): Name mapping - ドゴラ → Dogora (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商 → Slave Merchant (using translation)
GeminiTranslationService (2.0): Name mapping - 呪具 → Cursed Item (using translation)
GeminiTranslationService (2.0): Name mapping - 魔人 → Demon (using translation)
GeminiTranslationService (2.0): Name mapping - 闇天太陽 → Dark Sky Sun (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫、空閃 → Tenki, Kusen (Sky Princess, Sky Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - ワルガー → Walgar (using translation)
GeminiTranslationService (2.0): Name mapping - ハルトマン侯爵家 → Hartmann Marquisate Family (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア・ハルトマン → Shirlia Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ・レンジャー → Tilty Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド・レンジャー → Soldo Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 千里眼 → Clairvoyance (using translation)
Gemini20TranslationService: Attempt 1 of 3
Gemini20TranslationService: Using maxOutputTokens: 5120
Gemini20TranslationService: Non-retryable error, stopping retries
TranslationService: Provider failed, trying legacy Gemini fallback: HTTP error: 400
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Using maxOutputTokens: 8192
TranslationService: Gemini finishReason: STOP
TranslationService: API indicated completion with finish reason: STOP, skipping heuristic checks
TranslationService: Gemini language validation passed - detected english
TranslationService: Gemini dialogue tag issues detected: Added dialogue tags not present in original: he said, Some dialogue tags could not be automatically removed: he said, Significant change in dialogue quotation mark count: 326 → 9
TranslationService: Applied Gemini dialogue tag cleanup
TranslationService: Applying Gemini name substitutions with 182 dictionary entries
TranslationService: No Gemini name substitutions applied
TranslationService: Using AI Provider Manager with active provider: gemini_20
AIProviderManager: Using primary provider: gemini_20
GeminiTranslationService (2.0): Using name dictionary with 182 entries
GeminiTranslationService (2.0): Name mapping - 父さん → Tou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (2.0): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 母さん → Kaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お姉ちゃん → Onee-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お兄さん → Onii-san (using translation)
GeminiTranslationService (2.0): Name mapping - 剣聖 → Sword Saint (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力砲 → magic cannon (using translation)
GeminiTranslationService (2.0): Name mapping - 影 → Shadow (using translation)
GeminiTranslationService (2.0): Name mapping - タイタンズ → Titans (using translation)
GeminiTranslationService (2.0): Name mapping - 魔道具 → Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラゴン → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 回復アイテム → Healing Item (using translation)
GeminiTranslationService (2.0): Name mapping - アイン → Ain (using translation)
GeminiTranslationService (2.0): Name mapping - ライク → Laik (using translation)
GeminiTranslationService (2.0): Name mapping - 氷狼一閃 → Hyoro Issen (Ice Wolf Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流 → Majin Ryu (Demon God Style) (using translation)
GeminiTranslationService (2.0): Name mapping - 剣術 → Kenjutsu (Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法スキル → Magic Skill (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣 → Magic Beast (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流剣術 → Majin Ryu Kenjutsu (Demon God Style Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜炎斬 → Kokuryu Enzan (Black Dragon Flame Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョン → Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣災害 → Magic Beast Disaster (using translation)
GeminiTranslationService (2.0): Name mapping - 炎のブレス → Flame Breath (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼……一閃》 → 《Hyoro…Issen (Ice Wolf…Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼一閃》 → 《Hyoro Issen (Ice Wolf Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《黒竜炎斬》 → 《Kokuryu Enzan (Black Dragon Flame Slash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 紅蓮の炎 → Crimson Flame (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ → Tilty (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド → Soldo (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア → Shirlia (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家 → Marquis Household (using translation)
GeminiTranslationService (2.0): Name mapping - 《千里眼》 → Senrigan (Clairvoyance) (using translation)
GeminiTranslationService (2.0): Name mapping - ドラゴン → Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法 → Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力 → Magical Power (using translation)
GeminiTranslationService (2.0): Name mapping - 中立的な家臣達 → Neutral Retainers (using translation)
GeminiTranslationService (2.0): Name mapping - 対毒魔法 → Anti-Poison Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 妹達 → Imoto-tachi (using translation)
GeminiTranslationService (2.0): Name mapping - 次期当主 → Next Family Head (using translation)
GeminiTranslationService (2.0): Name mapping - シルリアさん → Shirlia-san (using translation)
GeminiTranslationService (2.0): Name mapping - 睡眠薬 → Sleeping pill (using translation)
GeminiTranslationService (2.0): Name mapping - ポヤポヤ → Fuzzy / Dazed / Absent-minded (using translation)
GeminiTranslationService (2.0): Name mapping - ホッ → Relief (sigh of relief) (using translation)
GeminiTranslationService (2.0): Name mapping - ゾッコン → Deeply in love / Infatuated (using translation)
GeminiTranslationService (2.0): Name mapping - メイド → Maid (using translation)
GeminiTranslationService (2.0): Name mapping - 心底ホッ → Deep Relief (using translation)
GeminiTranslationService (2.0): Name mapping - 腹黒メガネ → Scheming Glasses-wearer (using translation)
GeminiTranslationService (2.0): Name mapping - アインさん → Ain-san (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラ → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - アイテム → Item (using translation)
GeminiTranslationService (2.0): Name mapping - ダメージ → Damage (using translation)
GeminiTranslationService (2.0): Name mapping - リカバリー → Recovery (using translation)
GeminiTranslationService (2.0): Name mapping - プレイヤー → Player (using translation)
GeminiTranslationService (2.0): Name mapping - 炎球 → Fireball (using translation)
GeminiTranslationService (2.0): Name mapping - リスク → Risk (using translation)
GeminiTranslationService (2.0): Name mapping - ラゴン → ragon (using translation)
GeminiTranslationService (2.0): Name mapping - ライク様 → Laik-sama (using translation)
GeminiTranslationService (2.0): Name mapping - レンジャー領 → Ranger Territory (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士 → Knight (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商人 → Slave Trader (using translation)
GeminiTranslationService (2.0): Name mapping - ガストの町 → Gast Town (using translation)
GeminiTranslationService (2.0): Name mapping - 医務室 → Infirmary (using translation)
GeminiTranslationService (2.0): Name mapping - 支配の呪具 → Dominance Curse Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士団 → Knight Order (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵令嬢 → marquis’ daughter (using translation)
GeminiTranslationService (2.0): Name mapping - 闇巨人 → Dark Giant (using translation)
GeminiTranslationService (2.0): Name mapping - ガランド・レンジャー → Garand Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 王都 → Royal Capital (using translation)
GeminiTranslationService (2.0): Name mapping - 門 → Gate (using translation)
GeminiTranslationService (2.0): Name mapping - 二の型 → Second Form (using translation)
GeminiTranslationService (2.0): Name mapping - ライク・ハルトマン → Laik Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - 三の型 → Third Form (using translation)
GeminiTranslationService (2.0): Name mapping - ミラウ → Mirau (using translation)
GeminiTranslationService (2.0): Name mapping - フレイ → Frey (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫空閃 → Tenki Kusen (Celestial Princess Sky Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - 空間転移 → Spatial Transfer (using translation)
GeminiTranslationService (2.0): Name mapping - 剣士 → Swordsman (using translation)
GeminiTranslationService (2.0): Name mapping - 運び屋 → Carrier (using translation)
GeminiTranslationService (2.0): Name mapping - 訓練場 → Training Grounds (using translation)
GeminiTranslationService (2.0): Name mapping - パパ → Dad (using translation)
GeminiTranslationService (2.0): Name mapping - 氷華 → Ice Flower (using translation)
GeminiTranslationService (2.0): Name mapping - 師匠 → shisho (using translation)
GeminiTranslationService (2.0): Name mapping - 飛行 → Flight (using translation)
GeminiTranslationService (2.0): Name mapping - 風刃 → Wind Blade (using translation)
GeminiTranslationService (2.0): Name mapping - 好感度 → favorability (using translation)
GeminiTranslationService (2.0): Name mapping - 二刀流 → Dual-wielding (using translation)
GeminiTranslationService (2.0): Name mapping - 四の型 → Fourth Form (using translation)
GeminiTranslationService (2.0): Name mapping - 氷炎万華 → Hyoen Mange (Ice Flame Myriad Flowers) (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵家 → Baron's family (using translation)
GeminiTranslationService (2.0): Name mapping - 公爵家 → Duke's family (using translation)
GeminiTranslationService (2.0): Name mapping - 執事 → Butler (using translation)
GeminiTranslationService (2.0): Name mapping - テラード・マジェスター → Terrard Majester (using translation)
GeminiTranslationService (2.0): Name mapping - 闇組織 → Dark Organization (using translation)
GeminiTranslationService (2.0): Name mapping - 世界征服 → World Domination (using translation)
GeminiTranslationService (2.0): Name mapping - 従者 → Retainer (using translation)
GeminiTranslationService (2.0): Name mapping - 爵位 → Peerage/Title of Nobility (using translation)
GeminiTranslationService (2.0): Name mapping - 家格 → Rank (using translation)
GeminiTranslationService (2.0): Name mapping - 大道芸人 → Street Performer (using translation)
GeminiTranslationService (2.0): Name mapping - 王家 → Royal Family (using translation)
GeminiTranslationService (2.0): Name mapping - 学園 → Academy (using translation)
GeminiTranslationService (2.0): Name mapping - 魔石 → Magic Stone (using translation)
GeminiTranslationService (2.0): Name mapping - クラーク・ハルトマン → Clark Hartman (using translation)
GeminiTranslationService (2.0): Name mapping - 伯爵位 → Earldom (using translation)
GeminiTranslationService (2.0): Name mapping - 準貴族 → Quasi-Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 木っ端貴族 → Minor Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 刀 → Katana (using translation)
GeminiTranslationService (2.0): Name mapping - 応接室 → Reception Room (using translation)
GeminiTranslationService (2.0): Name mapping - 芸者 → Geisha (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト村 → Luruto Village (using translation)
GeminiTranslationService (2.0): Name mapping - 村長 → Village Chief (using translation)
GeminiTranslationService (2.0): Name mapping - コルス → Kols (using translation)
GeminiTranslationService (2.0): Name mapping - スフィア → Sufia (using translation)
GeminiTranslationService (2.0): Name mapping - ノブファンプレイヤー → Nobu Fan Player (using translation)
GeminiTranslationService (2.0): Name mapping - 聖属性 → Holy Attribute (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法学園 → Magic Academy (using translation)
GeminiTranslationService (2.0): Name mapping - クヴァト → Kuvato (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョンボス → Dungeon Boss (using translation)
GeminiTranslationService (2.0): Name mapping - 一の型 → First Form (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョ → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 獣タイプ → Beast Type (using translation)
GeminiTranslationService (2.0): Name mapping - 幽霊 → Ghost (using translation)
GeminiTranslationService (2.0): Name mapping - 剣舞 → Sword Dance (using translation)
GeminiTranslationService (2.0): Name mapping - 幽剣乱舞 → Yūken Ranbu (Phantom Sword Dance) (using translation)
GeminiTranslationService (2.0): Name mapping - ジャグリング → Juggling (using translation)
GeminiTranslationService (2.0): Name mapping - 村人 → Villager (using translation)
GeminiTranslationService (2.0): Name mapping - 五の型 → Fifth Form (using translation)
GeminiTranslationService (2.0): Name mapping - クロウ・デア・アルバート → Crow Der Albert (using translation)
GeminiTranslationService (2.0): Name mapping - アルバート王国 → Albert Kingdom (using translation)
GeminiTranslationService (2.0): Name mapping - 第一王子 → First Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 王家の“影” → Royal "Shadows" (using translation)
GeminiTranslationService (2.0): Name mapping - 製法 → Manufacturing Method (using translation)
GeminiTranslationService (2.0): Name mapping - ルイス → Louis (using translation)
GeminiTranslationService (2.0): Name mapping - 映写魔道具 → Projection Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 辺境の村 → Frontier Village (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト → Luruto (using translation)
GeminiTranslationService (2.0): Name mapping - 地図 → Map (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョン → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 木剣 → Wooden Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワー → Charisma (using translation)
GeminiTranslationService (2.0): Name mapping - 空間ごと隔絶 → separated space (using translation)
GeminiTranslationService (2.0): Name mapping - 闇魔法 → Dark Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワ → Charisma Power (using translation)
GeminiTranslationService (2.0): Name mapping - コボルト → Kobold (using translation)
GeminiTranslationService (2.0): Name mapping - 王子殿下 → Your Royal Highness / Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 攻略対象 → Capture Target (using translation)
GeminiTranslationService (2.0): Name mapping - チートキャラ → Cheat Character (using translation)
GeminiTranslationService (2.0): Name mapping - ゴブリン → Goblin (using translation)
GeminiTranslationService (2.0): Name mapping - ホブゴブリン → Hobgoblin (using translation)
GeminiTranslationService (2.0): Name mapping - 棍棒 → Club (using translation)
GeminiTranslationService (2.0): Name mapping - 幻の剣 → Phantom Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 宮廷剣術 → Court Swordsmanship (using translation)
GeminiTranslationService (2.0): Name mapping - マジェスター公爵家 → Majester Ducal House (using translation)
GeminiTranslationService (2.0): Name mapping - オープニングイベント → Opening Event (using translation)
GeminiTranslationService (2.0): Name mapping - 炎の剣 → Sword of Flame (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵 → baron (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜 → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - お父様 → Otou-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お母様 → Okaa-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お姉さん → Onee-san (using translation)
GeminiTranslationService (2.0): Name mapping - おじさん → Ojisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばさん → Obasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいさん → Ojiisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあさん → Obaasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいちゃん → Ojiichan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあちゃん → Obaachan (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家の屋敷 → marquis&#039;s estate (using translation)
GeminiTranslationService (2.0): Name mapping - ドゴラ → Dogora (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商 → Slave Merchant (using translation)
GeminiTranslationService (2.0): Name mapping - 呪具 → Cursed Item (using translation)
GeminiTranslationService (2.0): Name mapping - 魔人 → Demon (using translation)
GeminiTranslationService (2.0): Name mapping - 闇天太陽 → Dark Sky Sun (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫、空閃 → Tenki, Kusen (Sky Princess, Sky Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - ワルガー → Walgar (using translation)
GeminiTranslationService (2.0): Name mapping - ハルトマン侯爵家 → Hartmann Marquisate Family (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア・ハルトマン → Shirlia Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ・レンジャー → Tilty Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド・レンジャー → Soldo Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 千里眼 → Clairvoyance (using translation)
Gemini20TranslationService: Attempt 1 of 3
Gemini20TranslationService: Using maxOutputTokens: 5120
Gemini20TranslationService: Non-retryable error, stopping retries
TranslationService: Provider failed, trying legacy Gemini fallback: HTTP error: 400
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Using maxOutputTokens: 8192
TranslationService: Gemini finishReason: STOP
TranslationService: API indicated completion with finish reason: STOP, skipping heuristic checks
TranslationService: Gemini language validation passed - detected english
TranslationService: Gemini dialogue tag issues detected: Added dialogue tags not present in original: he said, Some dialogue tags could not be automatically removed: he said, Significant change in dialogue quotation mark count: 645 → 20, Dialogue line structure significantly changed: 16 → 10 lines with dialogue
TranslationService: Applied Gemini dialogue tag cleanup
TranslationService: Applying Gemini name substitutions with 182 dictionary entries
TranslationService: No Gemini name substitutions applied
TranslationService: Using AI Provider Manager with active provider: gemini_20
AIProviderManager: Using primary provider: gemini_20
GeminiTranslationService (2.0): Using name dictionary with 182 entries
GeminiTranslationService (2.0): Name mapping - 父さん → Tou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (2.0): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 母さん → Kaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お姉ちゃん → Onee-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お兄さん → Onii-san (using translation)
GeminiTranslationService (2.0): Name mapping - 剣聖 → Sword Saint (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力砲 → magic cannon (using translation)
GeminiTranslationService (2.0): Name mapping - 影 → Shadow (using translation)
GeminiTranslationService (2.0): Name mapping - タイタンズ → Titans (using translation)
GeminiTranslationService (2.0): Name mapping - 魔道具 → Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラゴン → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 回復アイテム → Healing Item (using translation)
GeminiTranslationService (2.0): Name mapping - アイン → Ain (using translation)
GeminiTranslationService (2.0): Name mapping - ライク → Laik (using translation)
GeminiTranslationService (2.0): Name mapping - 氷狼一閃 → Hyoro Issen (Ice Wolf Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流 → Majin Ryu (Demon God Style) (using translation)
GeminiTranslationService (2.0): Name mapping - 剣術 → Kenjutsu (Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法スキル → Magic Skill (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣 → Magic Beast (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流剣術 → Majin Ryu Kenjutsu (Demon God Style Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜炎斬 → Kokuryu Enzan (Black Dragon Flame Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョン → Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣災害 → Magic Beast Disaster (using translation)
GeminiTranslationService (2.0): Name mapping - 炎のブレス → Flame Breath (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼……一閃》 → 《Hyoro…Issen (Ice Wolf…Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼一閃》 → 《Hyoro Issen (Ice Wolf Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《黒竜炎斬》 → 《Kokuryu Enzan (Black Dragon Flame Slash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 紅蓮の炎 → Crimson Flame (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ → Tilty (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド → Soldo (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア → Shirlia (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家 → Marquis Household (using translation)
GeminiTranslationService (2.0): Name mapping - 《千里眼》 → Senrigan (Clairvoyance) (using translation)
GeminiTranslationService (2.0): Name mapping - ドラゴン → Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法 → Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力 → Magical Power (using translation)
GeminiTranslationService (2.0): Name mapping - 中立的な家臣達 → Neutral Retainers (using translation)
GeminiTranslationService (2.0): Name mapping - 対毒魔法 → Anti-Poison Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 妹達 → Imoto-tachi (using translation)
GeminiTranslationService (2.0): Name mapping - 次期当主 → Next Family Head (using translation)
GeminiTranslationService (2.0): Name mapping - シルリアさん → Shirlia-san (using translation)
GeminiTranslationService (2.0): Name mapping - 睡眠薬 → Sleeping pill (using translation)
GeminiTranslationService (2.0): Name mapping - ポヤポヤ → Fuzzy / Dazed / Absent-minded (using translation)
GeminiTranslationService (2.0): Name mapping - ホッ → Relief (sigh of relief) (using translation)
GeminiTranslationService (2.0): Name mapping - ゾッコン → Deeply in love / Infatuated (using translation)
GeminiTranslationService (2.0): Name mapping - メイド → Maid (using translation)
GeminiTranslationService (2.0): Name mapping - 心底ホッ → Deep Relief (using translation)
GeminiTranslationService (2.0): Name mapping - 腹黒メガネ → Scheming Glasses-wearer (using translation)
GeminiTranslationService (2.0): Name mapping - アインさん → Ain-san (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラ → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - アイテム → Item (using translation)
GeminiTranslationService (2.0): Name mapping - ダメージ → Damage (using translation)
GeminiTranslationService (2.0): Name mapping - リカバリー → Recovery (using translation)
GeminiTranslationService (2.0): Name mapping - プレイヤー → Player (using translation)
GeminiTranslationService (2.0): Name mapping - 炎球 → Fireball (using translation)
GeminiTranslationService (2.0): Name mapping - リスク → Risk (using translation)
GeminiTranslationService (2.0): Name mapping - ラゴン → ragon (using translation)
GeminiTranslationService (2.0): Name mapping - ライク様 → Laik-sama (using translation)
GeminiTranslationService (2.0): Name mapping - レンジャー領 → Ranger Territory (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士 → Knight (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商人 → Slave Trader (using translation)
GeminiTranslationService (2.0): Name mapping - ガストの町 → Gast Town (using translation)
GeminiTranslationService (2.0): Name mapping - 医務室 → Infirmary (using translation)
GeminiTranslationService (2.0): Name mapping - 支配の呪具 → Dominance Curse Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士団 → Knight Order (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵令嬢 → marquis’ daughter (using translation)
GeminiTranslationService (2.0): Name mapping - 闇巨人 → Dark Giant (using translation)
GeminiTranslationService (2.0): Name mapping - ガランド・レンジャー → Garand Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 王都 → Royal Capital (using translation)
GeminiTranslationService (2.0): Name mapping - 門 → Gate (using translation)
GeminiTranslationService (2.0): Name mapping - 二の型 → Second Form (using translation)
GeminiTranslationService (2.0): Name mapping - ライク・ハルトマン → Laik Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - 三の型 → Third Form (using translation)
GeminiTranslationService (2.0): Name mapping - ミラウ → Mirau (using translation)
GeminiTranslationService (2.0): Name mapping - フレイ → Frey (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫空閃 → Tenki Kusen (Celestial Princess Sky Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - 空間転移 → Spatial Transfer (using translation)
GeminiTranslationService (2.0): Name mapping - 剣士 → Swordsman (using translation)
GeminiTranslationService (2.0): Name mapping - 運び屋 → Carrier (using translation)
GeminiTranslationService (2.0): Name mapping - 訓練場 → Training Grounds (using translation)
GeminiTranslationService (2.0): Name mapping - パパ → Dad (using translation)
GeminiTranslationService (2.0): Name mapping - 氷華 → Ice Flower (using translation)
GeminiTranslationService (2.0): Name mapping - 師匠 → shisho (using translation)
GeminiTranslationService (2.0): Name mapping - 飛行 → Flight (using translation)
GeminiTranslationService (2.0): Name mapping - 風刃 → Wind Blade (using translation)
GeminiTranslationService (2.0): Name mapping - 好感度 → favorability (using translation)
GeminiTranslationService (2.0): Name mapping - 二刀流 → Dual-wielding (using translation)
GeminiTranslationService (2.0): Name mapping - 四の型 → Fourth Form (using translation)
GeminiTranslationService (2.0): Name mapping - 氷炎万華 → Hyoen Mange (Ice Flame Myriad Flowers) (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵家 → Baron's family (using translation)
GeminiTranslationService (2.0): Name mapping - 公爵家 → Duke's family (using translation)
GeminiTranslationService (2.0): Name mapping - 執事 → Butler (using translation)
GeminiTranslationService (2.0): Name mapping - テラード・マジェスター → Terrard Majester (using translation)
GeminiTranslationService (2.0): Name mapping - 闇組織 → Dark Organization (using translation)
GeminiTranslationService (2.0): Name mapping - 世界征服 → World Domination (using translation)
GeminiTranslationService (2.0): Name mapping - 従者 → Retainer (using translation)
GeminiTranslationService (2.0): Name mapping - 爵位 → Peerage/Title of Nobility (using translation)
GeminiTranslationService (2.0): Name mapping - 家格 → Rank (using translation)
GeminiTranslationService (2.0): Name mapping - 大道芸人 → Street Performer (using translation)
GeminiTranslationService (2.0): Name mapping - 王家 → Royal Family (using translation)
GeminiTranslationService (2.0): Name mapping - 学園 → Academy (using translation)
GeminiTranslationService (2.0): Name mapping - 魔石 → Magic Stone (using translation)
GeminiTranslationService (2.0): Name mapping - クラーク・ハルトマン → Clark Hartman (using translation)
GeminiTranslationService (2.0): Name mapping - 伯爵位 → Earldom (using translation)
GeminiTranslationService (2.0): Name mapping - 準貴族 → Quasi-Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 木っ端貴族 → Minor Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 刀 → Katana (using translation)
GeminiTranslationService (2.0): Name mapping - 応接室 → Reception Room (using translation)
GeminiTranslationService (2.0): Name mapping - 芸者 → Geisha (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト村 → Luruto Village (using translation)
GeminiTranslationService (2.0): Name mapping - 村長 → Village Chief (using translation)
GeminiTranslationService (2.0): Name mapping - コルス → Kols (using translation)
GeminiTranslationService (2.0): Name mapping - スフィア → Sufia (using translation)
GeminiTranslationService (2.0): Name mapping - ノブファンプレイヤー → Nobu Fan Player (using translation)
GeminiTranslationService (2.0): Name mapping - 聖属性 → Holy Attribute (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法学園 → Magic Academy (using translation)
GeminiTranslationService (2.0): Name mapping - クヴァト → Kuvato (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョンボス → Dungeon Boss (using translation)
GeminiTranslationService (2.0): Name mapping - 一の型 → First Form (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョ → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 獣タイプ → Beast Type (using translation)
GeminiTranslationService (2.0): Name mapping - 幽霊 → Ghost (using translation)
GeminiTranslationService (2.0): Name mapping - 剣舞 → Sword Dance (using translation)
GeminiTranslationService (2.0): Name mapping - 幽剣乱舞 → Yūken Ranbu (Phantom Sword Dance) (using translation)
GeminiTranslationService (2.0): Name mapping - ジャグリング → Juggling (using translation)
GeminiTranslationService (2.0): Name mapping - 村人 → Villager (using translation)
GeminiTranslationService (2.0): Name mapping - 五の型 → Fifth Form (using translation)
GeminiTranslationService (2.0): Name mapping - クロウ・デア・アルバート → Crow Der Albert (using translation)
GeminiTranslationService (2.0): Name mapping - アルバート王国 → Albert Kingdom (using translation)
GeminiTranslationService (2.0): Name mapping - 第一王子 → First Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 王家の“影” → Royal "Shadows" (using translation)
GeminiTranslationService (2.0): Name mapping - 製法 → Manufacturing Method (using translation)
GeminiTranslationService (2.0): Name mapping - ルイス → Louis (using translation)
GeminiTranslationService (2.0): Name mapping - 映写魔道具 → Projection Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 辺境の村 → Frontier Village (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト → Luruto (using translation)
GeminiTranslationService (2.0): Name mapping - 地図 → Map (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョン → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 木剣 → Wooden Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワー → Charisma (using translation)
GeminiTranslationService (2.0): Name mapping - 空間ごと隔絶 → separated space (using translation)
GeminiTranslationService (2.0): Name mapping - 闇魔法 → Dark Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワ → Charisma Power (using translation)
GeminiTranslationService (2.0): Name mapping - コボルト → Kobold (using translation)
GeminiTranslationService (2.0): Name mapping - 王子殿下 → Your Royal Highness / Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 攻略対象 → Capture Target (using translation)
GeminiTranslationService (2.0): Name mapping - チートキャラ → Cheat Character (using translation)
GeminiTranslationService (2.0): Name mapping - ゴブリン → Goblin (using translation)
GeminiTranslationService (2.0): Name mapping - ホブゴブリン → Hobgoblin (using translation)
GeminiTranslationService (2.0): Name mapping - 棍棒 → Club (using translation)
GeminiTranslationService (2.0): Name mapping - 幻の剣 → Phantom Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 宮廷剣術 → Court Swordsmanship (using translation)
GeminiTranslationService (2.0): Name mapping - マジェスター公爵家 → Majester Ducal House (using translation)
GeminiTranslationService (2.0): Name mapping - オープニングイベント → Opening Event (using translation)
GeminiTranslationService (2.0): Name mapping - 炎の剣 → Sword of Flame (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵 → baron (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜 → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - お父様 → Otou-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お母様 → Okaa-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お姉さん → Onee-san (using translation)
GeminiTranslationService (2.0): Name mapping - おじさん → Ojisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばさん → Obasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいさん → Ojiisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあさん → Obaasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいちゃん → Ojiichan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあちゃん → Obaachan (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家の屋敷 → marquis&#039;s estate (using translation)
GeminiTranslationService (2.0): Name mapping - ドゴラ → Dogora (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商 → Slave Merchant (using translation)
GeminiTranslationService (2.0): Name mapping - 呪具 → Cursed Item (using translation)
GeminiTranslationService (2.0): Name mapping - 魔人 → Demon (using translation)
GeminiTranslationService (2.0): Name mapping - 闇天太陽 → Dark Sky Sun (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫、空閃 → Tenki, Kusen (Sky Princess, Sky Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - ワルガー → Walgar (using translation)
GeminiTranslationService (2.0): Name mapping - ハルトマン侯爵家 → Hartmann Marquisate Family (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア・ハルトマン → Shirlia Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ・レンジャー → Tilty Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド・レンジャー → Soldo Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 千里眼 → Clairvoyance (using translation)
Gemini20TranslationService: Attempt 1 of 3
Gemini20TranslationService: Using maxOutputTokens: 5120
Gemini20TranslationService: Non-retryable error, stopping retries
TranslationService: Provider failed, trying legacy Gemini fallback: HTTP error: 400
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Using maxOutputTokens: 8192
TranslationService: Gemini finishReason: STOP
TranslationService: API indicated completion with finish reason: STOP, skipping heuristic checks
TranslationService: Gemini language validation passed - detected english
TranslationService: Gemini dialogue tag issues detected: Added dialogue tags not present in original: he said, Some dialogue tags could not be automatically removed: he said, Significant change in dialogue quotation mark count: 645 → 20, Dialogue line structure significantly changed: 16 → 10 lines with dialogue
TranslationService: Applied Gemini dialogue tag cleanup
TranslationService: Applying Gemini name substitutions with 182 dictionary entries
TranslationService: No Gemini name substitutions applied
TranslationService: Using AI Provider Manager with active provider: gemini_20
AIProviderManager: Using primary provider: gemini_20
GeminiTranslationService (2.0): Using name dictionary with 182 entries
GeminiTranslationService (2.0): Name mapping - 父さん → Tou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (2.0): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 母さん → Kaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お姉ちゃん → Onee-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お兄さん → Onii-san (using translation)
GeminiTranslationService (2.0): Name mapping - 剣聖 → Sword Saint (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力砲 → magic cannon (using translation)
GeminiTranslationService (2.0): Name mapping - 影 → Shadow (using translation)
GeminiTranslationService (2.0): Name mapping - タイタンズ → Titans (using translation)
GeminiTranslationService (2.0): Name mapping - 魔道具 → Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラゴン → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 回復アイテム → Healing Item (using translation)
GeminiTranslationService (2.0): Name mapping - アイン → Ain (using translation)
GeminiTranslationService (2.0): Name mapping - ライク → Laik (using translation)
GeminiTranslationService (2.0): Name mapping - 氷狼一閃 → Hyoro Issen (Ice Wolf Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流 → Majin Ryu (Demon God Style) (using translation)
GeminiTranslationService (2.0): Name mapping - 剣術 → Kenjutsu (Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法スキル → Magic Skill (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣 → Magic Beast (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流剣術 → Majin Ryu Kenjutsu (Demon God Style Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜炎斬 → Kokuryu Enzan (Black Dragon Flame Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョン → Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣災害 → Magic Beast Disaster (using translation)
GeminiTranslationService (2.0): Name mapping - 炎のブレス → Flame Breath (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼……一閃》 → 《Hyoro…Issen (Ice Wolf…Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼一閃》 → 《Hyoro Issen (Ice Wolf Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《黒竜炎斬》 → 《Kokuryu Enzan (Black Dragon Flame Slash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 紅蓮の炎 → Crimson Flame (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ → Tilty (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド → Soldo (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア → Shirlia (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家 → Marquis Household (using translation)
GeminiTranslationService (2.0): Name mapping - 《千里眼》 → Senrigan (Clairvoyance) (using translation)
GeminiTranslationService (2.0): Name mapping - ドラゴン → Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法 → Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力 → Magical Power (using translation)
GeminiTranslationService (2.0): Name mapping - 中立的な家臣達 → Neutral Retainers (using translation)
GeminiTranslationService (2.0): Name mapping - 対毒魔法 → Anti-Poison Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 妹達 → Imoto-tachi (using translation)
GeminiTranslationService (2.0): Name mapping - 次期当主 → Next Family Head (using translation)
GeminiTranslationService (2.0): Name mapping - シルリアさん → Shirlia-san (using translation)
GeminiTranslationService (2.0): Name mapping - 睡眠薬 → Sleeping pill (using translation)
GeminiTranslationService (2.0): Name mapping - ポヤポヤ → Fuzzy / Dazed / Absent-minded (using translation)
GeminiTranslationService (2.0): Name mapping - ホッ → Relief (sigh of relief) (using translation)
GeminiTranslationService (2.0): Name mapping - ゾッコン → Deeply in love / Infatuated (using translation)
GeminiTranslationService (2.0): Name mapping - メイド → Maid (using translation)
GeminiTranslationService (2.0): Name mapping - 心底ホッ → Deep Relief (using translation)
GeminiTranslationService (2.0): Name mapping - 腹黒メガネ → Scheming Glasses-wearer (using translation)
GeminiTranslationService (2.0): Name mapping - アインさん → Ain-san (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラ → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - アイテム → Item (using translation)
GeminiTranslationService (2.0): Name mapping - ダメージ → Damage (using translation)
GeminiTranslationService (2.0): Name mapping - リカバリー → Recovery (using translation)
GeminiTranslationService (2.0): Name mapping - プレイヤー → Player (using translation)
GeminiTranslationService (2.0): Name mapping - 炎球 → Fireball (using translation)
GeminiTranslationService (2.0): Name mapping - リスク → Risk (using translation)
GeminiTranslationService (2.0): Name mapping - ラゴン → ragon (using translation)
GeminiTranslationService (2.0): Name mapping - ライク様 → Laik-sama (using translation)
GeminiTranslationService (2.0): Name mapping - レンジャー領 → Ranger Territory (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士 → Knight (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商人 → Slave Trader (using translation)
GeminiTranslationService (2.0): Name mapping - ガストの町 → Gast Town (using translation)
GeminiTranslationService (2.0): Name mapping - 医務室 → Infirmary (using translation)
GeminiTranslationService (2.0): Name mapping - 支配の呪具 → Dominance Curse Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士団 → Knight Order (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵令嬢 → marquis’ daughter (using translation)
GeminiTranslationService (2.0): Name mapping - 闇巨人 → Dark Giant (using translation)
GeminiTranslationService (2.0): Name mapping - ガランド・レンジャー → Garand Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 王都 → Royal Capital (using translation)
GeminiTranslationService (2.0): Name mapping - 門 → Gate (using translation)
GeminiTranslationService (2.0): Name mapping - 二の型 → Second Form (using translation)
GeminiTranslationService (2.0): Name mapping - ライク・ハルトマン → Laik Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - 三の型 → Third Form (using translation)
GeminiTranslationService (2.0): Name mapping - ミラウ → Mirau (using translation)
GeminiTranslationService (2.0): Name mapping - フレイ → Frey (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫空閃 → Tenki Kusen (Celestial Princess Sky Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - 空間転移 → Spatial Transfer (using translation)
GeminiTranslationService (2.0): Name mapping - 剣士 → Swordsman (using translation)
GeminiTranslationService (2.0): Name mapping - 運び屋 → Carrier (using translation)
GeminiTranslationService (2.0): Name mapping - 訓練場 → Training Grounds (using translation)
GeminiTranslationService (2.0): Name mapping - パパ → Dad (using translation)
GeminiTranslationService (2.0): Name mapping - 氷華 → Ice Flower (using translation)
GeminiTranslationService (2.0): Name mapping - 師匠 → shisho (using translation)
GeminiTranslationService (2.0): Name mapping - 飛行 → Flight (using translation)
GeminiTranslationService (2.0): Name mapping - 風刃 → Wind Blade (using translation)
GeminiTranslationService (2.0): Name mapping - 好感度 → favorability (using translation)
GeminiTranslationService (2.0): Name mapping - 二刀流 → Dual-wielding (using translation)
GeminiTranslationService (2.0): Name mapping - 四の型 → Fourth Form (using translation)
GeminiTranslationService (2.0): Name mapping - 氷炎万華 → Hyoen Mange (Ice Flame Myriad Flowers) (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵家 → Baron's family (using translation)
GeminiTranslationService (2.0): Name mapping - 公爵家 → Duke's family (using translation)
GeminiTranslationService (2.0): Name mapping - 執事 → Butler (using translation)
GeminiTranslationService (2.0): Name mapping - テラード・マジェスター → Terrard Majester (using translation)
GeminiTranslationService (2.0): Name mapping - 闇組織 → Dark Organization (using translation)
GeminiTranslationService (2.0): Name mapping - 世界征服 → World Domination (using translation)
GeminiTranslationService (2.0): Name mapping - 従者 → Retainer (using translation)
GeminiTranslationService (2.0): Name mapping - 爵位 → Peerage/Title of Nobility (using translation)
GeminiTranslationService (2.0): Name mapping - 家格 → Rank (using translation)
GeminiTranslationService (2.0): Name mapping - 大道芸人 → Street Performer (using translation)
GeminiTranslationService (2.0): Name mapping - 王家 → Royal Family (using translation)
GeminiTranslationService (2.0): Name mapping - 学園 → Academy (using translation)
GeminiTranslationService (2.0): Name mapping - 魔石 → Magic Stone (using translation)
GeminiTranslationService (2.0): Name mapping - クラーク・ハルトマン → Clark Hartman (using translation)
GeminiTranslationService (2.0): Name mapping - 伯爵位 → Earldom (using translation)
GeminiTranslationService (2.0): Name mapping - 準貴族 → Quasi-Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 木っ端貴族 → Minor Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 刀 → Katana (using translation)
GeminiTranslationService (2.0): Name mapping - 応接室 → Reception Room (using translation)
GeminiTranslationService (2.0): Name mapping - 芸者 → Geisha (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト村 → Luruto Village (using translation)
GeminiTranslationService (2.0): Name mapping - 村長 → Village Chief (using translation)
GeminiTranslationService (2.0): Name mapping - コルス → Kols (using translation)
GeminiTranslationService (2.0): Name mapping - スフィア → Sufia (using translation)
GeminiTranslationService (2.0): Name mapping - ノブファンプレイヤー → Nobu Fan Player (using translation)
GeminiTranslationService (2.0): Name mapping - 聖属性 → Holy Attribute (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法学園 → Magic Academy (using translation)
GeminiTranslationService (2.0): Name mapping - クヴァト → Kuvato (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョンボス → Dungeon Boss (using translation)
GeminiTranslationService (2.0): Name mapping - 一の型 → First Form (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョ → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 獣タイプ → Beast Type (using translation)
GeminiTranslationService (2.0): Name mapping - 幽霊 → Ghost (using translation)
GeminiTranslationService (2.0): Name mapping - 剣舞 → Sword Dance (using translation)
GeminiTranslationService (2.0): Name mapping - 幽剣乱舞 → Yūken Ranbu (Phantom Sword Dance) (using translation)
GeminiTranslationService (2.0): Name mapping - ジャグリング → Juggling (using translation)
GeminiTranslationService (2.0): Name mapping - 村人 → Villager (using translation)
GeminiTranslationService (2.0): Name mapping - 五の型 → Fifth Form (using translation)
GeminiTranslationService (2.0): Name mapping - クロウ・デア・アルバート → Crow Der Albert (using translation)
GeminiTranslationService (2.0): Name mapping - アルバート王国 → Albert Kingdom (using translation)
GeminiTranslationService (2.0): Name mapping - 第一王子 → First Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 王家の“影” → Royal "Shadows" (using translation)
GeminiTranslationService (2.0): Name mapping - 製法 → Manufacturing Method (using translation)
GeminiTranslationService (2.0): Name mapping - ルイス → Louis (using translation)
GeminiTranslationService (2.0): Name mapping - 映写魔道具 → Projection Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 辺境の村 → Frontier Village (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト → Luruto (using translation)
GeminiTranslationService (2.0): Name mapping - 地図 → Map (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョン → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 木剣 → Wooden Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワー → Charisma (using translation)
GeminiTranslationService (2.0): Name mapping - 空間ごと隔絶 → separated space (using translation)
GeminiTranslationService (2.0): Name mapping - 闇魔法 → Dark Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワ → Charisma Power (using translation)
GeminiTranslationService (2.0): Name mapping - コボルト → Kobold (using translation)
GeminiTranslationService (2.0): Name mapping - 王子殿下 → Your Royal Highness / Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 攻略対象 → Capture Target (using translation)
GeminiTranslationService (2.0): Name mapping - チートキャラ → Cheat Character (using translation)
GeminiTranslationService (2.0): Name mapping - ゴブリン → Goblin (using translation)
GeminiTranslationService (2.0): Name mapping - ホブゴブリン → Hobgoblin (using translation)
GeminiTranslationService (2.0): Name mapping - 棍棒 → Club (using translation)
GeminiTranslationService (2.0): Name mapping - 幻の剣 → Phantom Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 宮廷剣術 → Court Swordsmanship (using translation)
GeminiTranslationService (2.0): Name mapping - マジェスター公爵家 → Majester Ducal House (using translation)
GeminiTranslationService (2.0): Name mapping - オープニングイベント → Opening Event (using translation)
GeminiTranslationService (2.0): Name mapping - 炎の剣 → Sword of Flame (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵 → baron (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜 → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - お父様 → Otou-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お母様 → Okaa-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お姉さん → Onee-san (using translation)
GeminiTranslationService (2.0): Name mapping - おじさん → Ojisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばさん → Obasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいさん → Ojiisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあさん → Obaasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいちゃん → Ojiichan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあちゃん → Obaachan (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家の屋敷 → marquis&#039;s estate (using translation)
GeminiTranslationService (2.0): Name mapping - ドゴラ → Dogora (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商 → Slave Merchant (using translation)
GeminiTranslationService (2.0): Name mapping - 呪具 → Cursed Item (using translation)
GeminiTranslationService (2.0): Name mapping - 魔人 → Demon (using translation)
GeminiTranslationService (2.0): Name mapping - 闇天太陽 → Dark Sky Sun (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫、空閃 → Tenki, Kusen (Sky Princess, Sky Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - ワルガー → Walgar (using translation)
GeminiTranslationService (2.0): Name mapping - ハルトマン侯爵家 → Hartmann Marquisate Family (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア・ハルトマン → Shirlia Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ・レンジャー → Tilty Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド・レンジャー → Soldo Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 千里眼 → Clairvoyance (using translation)
Gemini20TranslationService: Attempt 1 of 3
Gemini20TranslationService: Using maxOutputTokens: 5120
Gemini20TranslationService: Non-retryable error, stopping retries
TranslationService: Provider failed, trying legacy Gemini fallback: HTTP error: 400
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Using maxOutputTokens: 8192
TranslationService: Gemini finishReason: STOP
TranslationService: API indicated completion with finish reason: STOP, skipping heuristic checks
TranslationService: Gemini language validation passed - detected english
TranslationService: Gemini dialogue tag issues detected: Significant change in dialogue quotation mark count: 614 → 33
TranslationService: Applied Gemini dialogue tag cleanup
TranslationService: Applying Gemini name substitutions with 182 dictionary entries
TranslationService: Applied 1 Gemini name substitutions
NameSubstitutionService: Applied 1 name substitutions in Gemini fallback translation
  - 魔力 → Magical Power (found variations: magical power, replacements: 3)
TranslationService: Using AI Provider Manager with active provider: gemini_20
AIProviderManager: Using primary provider: gemini_20
GeminiTranslationService (2.0): Using name dictionary with 182 entries
GeminiTranslationService (2.0): Name mapping - 父さん → Tou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (2.0): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 母さん → Kaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お姉ちゃん → Onee-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お兄さん → Onii-san (using translation)
GeminiTranslationService (2.0): Name mapping - 剣聖 → Sword Saint (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力砲 → magic cannon (using translation)
GeminiTranslationService (2.0): Name mapping - 影 → Shadow (using translation)
GeminiTranslationService (2.0): Name mapping - タイタンズ → Titans (using translation)
GeminiTranslationService (2.0): Name mapping - 魔道具 → Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラゴン → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 回復アイテム → Healing Item (using translation)
GeminiTranslationService (2.0): Name mapping - アイン → Ain (using translation)
GeminiTranslationService (2.0): Name mapping - ライク → Laik (using translation)
GeminiTranslationService (2.0): Name mapping - 氷狼一閃 → Hyoro Issen (Ice Wolf Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流 → Majin Ryu (Demon God Style) (using translation)
GeminiTranslationService (2.0): Name mapping - 剣術 → Kenjutsu (Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法スキル → Magic Skill (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣 → Magic Beast (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流剣術 → Majin Ryu Kenjutsu (Demon God Style Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜炎斬 → Kokuryu Enzan (Black Dragon Flame Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョン → Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣災害 → Magic Beast Disaster (using translation)
GeminiTranslationService (2.0): Name mapping - 炎のブレス → Flame Breath (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼……一閃》 → 《Hyoro…Issen (Ice Wolf…Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼一閃》 → 《Hyoro Issen (Ice Wolf Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《黒竜炎斬》 → 《Kokuryu Enzan (Black Dragon Flame Slash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 紅蓮の炎 → Crimson Flame (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ → Tilty (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド → Soldo (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア → Shirlia (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家 → Marquis Household (using translation)
GeminiTranslationService (2.0): Name mapping - 《千里眼》 → Senrigan (Clairvoyance) (using translation)
GeminiTranslationService (2.0): Name mapping - ドラゴン → Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法 → Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力 → Magical Power (using translation)
GeminiTranslationService (2.0): Name mapping - 中立的な家臣達 → Neutral Retainers (using translation)
GeminiTranslationService (2.0): Name mapping - 対毒魔法 → Anti-Poison Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 妹達 → Imoto-tachi (using translation)
GeminiTranslationService (2.0): Name mapping - 次期当主 → Next Family Head (using translation)
GeminiTranslationService (2.0): Name mapping - シルリアさん → Shirlia-san (using translation)
GeminiTranslationService (2.0): Name mapping - 睡眠薬 → Sleeping pill (using translation)
GeminiTranslationService (2.0): Name mapping - ポヤポヤ → Fuzzy / Dazed / Absent-minded (using translation)
GeminiTranslationService (2.0): Name mapping - ホッ → Relief (sigh of relief) (using translation)
GeminiTranslationService (2.0): Name mapping - ゾッコン → Deeply in love / Infatuated (using translation)
GeminiTranslationService (2.0): Name mapping - メイド → Maid (using translation)
GeminiTranslationService (2.0): Name mapping - 心底ホッ → Deep Relief (using translation)
GeminiTranslationService (2.0): Name mapping - 腹黒メガネ → Scheming Glasses-wearer (using translation)
GeminiTranslationService (2.0): Name mapping - アインさん → Ain-san (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラ → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - アイテム → Item (using translation)
GeminiTranslationService (2.0): Name mapping - ダメージ → Damage (using translation)
GeminiTranslationService (2.0): Name mapping - リカバリー → Recovery (using translation)
GeminiTranslationService (2.0): Name mapping - プレイヤー → Player (using translation)
GeminiTranslationService (2.0): Name mapping - 炎球 → Fireball (using translation)
GeminiTranslationService (2.0): Name mapping - リスク → Risk (using translation)
GeminiTranslationService (2.0): Name mapping - ラゴン → ragon (using translation)
GeminiTranslationService (2.0): Name mapping - ライク様 → Laik-sama (using translation)
GeminiTranslationService (2.0): Name mapping - レンジャー領 → Ranger Territory (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士 → Knight (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商人 → Slave Trader (using translation)
GeminiTranslationService (2.0): Name mapping - ガストの町 → Gast Town (using translation)
GeminiTranslationService (2.0): Name mapping - 医務室 → Infirmary (using translation)
GeminiTranslationService (2.0): Name mapping - 支配の呪具 → Dominance Curse Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士団 → Knight Order (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵令嬢 → marquis’ daughter (using translation)
GeminiTranslationService (2.0): Name mapping - 闇巨人 → Dark Giant (using translation)
GeminiTranslationService (2.0): Name mapping - ガランド・レンジャー → Garand Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 王都 → Royal Capital (using translation)
GeminiTranslationService (2.0): Name mapping - 門 → Gate (using translation)
GeminiTranslationService (2.0): Name mapping - 二の型 → Second Form (using translation)
GeminiTranslationService (2.0): Name mapping - ライク・ハルトマン → Laik Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - 三の型 → Third Form (using translation)
GeminiTranslationService (2.0): Name mapping - ミラウ → Mirau (using translation)
GeminiTranslationService (2.0): Name mapping - フレイ → Frey (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫空閃 → Tenki Kusen (Celestial Princess Sky Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - 空間転移 → Spatial Transfer (using translation)
GeminiTranslationService (2.0): Name mapping - 剣士 → Swordsman (using translation)
GeminiTranslationService (2.0): Name mapping - 運び屋 → Carrier (using translation)
GeminiTranslationService (2.0): Name mapping - 訓練場 → Training Grounds (using translation)
GeminiTranslationService (2.0): Name mapping - パパ → Dad (using translation)
GeminiTranslationService (2.0): Name mapping - 氷華 → Ice Flower (using translation)
GeminiTranslationService (2.0): Name mapping - 師匠 → shisho (using translation)
GeminiTranslationService (2.0): Name mapping - 飛行 → Flight (using translation)
GeminiTranslationService (2.0): Name mapping - 風刃 → Wind Blade (using translation)
GeminiTranslationService (2.0): Name mapping - 好感度 → favorability (using translation)
GeminiTranslationService (2.0): Name mapping - 二刀流 → Dual-wielding (using translation)
GeminiTranslationService (2.0): Name mapping - 四の型 → Fourth Form (using translation)
GeminiTranslationService (2.0): Name mapping - 氷炎万華 → Hyoen Mange (Ice Flame Myriad Flowers) (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵家 → Baron's family (using translation)
GeminiTranslationService (2.0): Name mapping - 公爵家 → Duke's family (using translation)
GeminiTranslationService (2.0): Name mapping - 執事 → Butler (using translation)
GeminiTranslationService (2.0): Name mapping - テラード・マジェスター → Terrard Majester (using translation)
GeminiTranslationService (2.0): Name mapping - 闇組織 → Dark Organization (using translation)
GeminiTranslationService (2.0): Name mapping - 世界征服 → World Domination (using translation)
GeminiTranslationService (2.0): Name mapping - 従者 → Retainer (using translation)
GeminiTranslationService (2.0): Name mapping - 爵位 → Peerage/Title of Nobility (using translation)
GeminiTranslationService (2.0): Name mapping - 家格 → Rank (using translation)
GeminiTranslationService (2.0): Name mapping - 大道芸人 → Street Performer (using translation)
GeminiTranslationService (2.0): Name mapping - 王家 → Royal Family (using translation)
GeminiTranslationService (2.0): Name mapping - 学園 → Academy (using translation)
GeminiTranslationService (2.0): Name mapping - 魔石 → Magic Stone (using translation)
GeminiTranslationService (2.0): Name mapping - クラーク・ハルトマン → Clark Hartman (using translation)
GeminiTranslationService (2.0): Name mapping - 伯爵位 → Earldom (using translation)
GeminiTranslationService (2.0): Name mapping - 準貴族 → Quasi-Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 木っ端貴族 → Minor Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 刀 → Katana (using translation)
GeminiTranslationService (2.0): Name mapping - 応接室 → Reception Room (using translation)
GeminiTranslationService (2.0): Name mapping - 芸者 → Geisha (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト村 → Luruto Village (using translation)
GeminiTranslationService (2.0): Name mapping - 村長 → Village Chief (using translation)
GeminiTranslationService (2.0): Name mapping - コルス → Kols (using translation)
GeminiTranslationService (2.0): Name mapping - スフィア → Sufia (using translation)
GeminiTranslationService (2.0): Name mapping - ノブファンプレイヤー → Nobu Fan Player (using translation)
GeminiTranslationService (2.0): Name mapping - 聖属性 → Holy Attribute (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法学園 → Magic Academy (using translation)
GeminiTranslationService (2.0): Name mapping - クヴァト → Kuvato (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョンボス → Dungeon Boss (using translation)
GeminiTranslationService (2.0): Name mapping - 一の型 → First Form (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョ → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 獣タイプ → Beast Type (using translation)
GeminiTranslationService (2.0): Name mapping - 幽霊 → Ghost (using translation)
GeminiTranslationService (2.0): Name mapping - 剣舞 → Sword Dance (using translation)
GeminiTranslationService (2.0): Name mapping - 幽剣乱舞 → Yūken Ranbu (Phantom Sword Dance) (using translation)
GeminiTranslationService (2.0): Name mapping - ジャグリング → Juggling (using translation)
GeminiTranslationService (2.0): Name mapping - 村人 → Villager (using translation)
GeminiTranslationService (2.0): Name mapping - 五の型 → Fifth Form (using translation)
GeminiTranslationService (2.0): Name mapping - クロウ・デア・アルバート → Crow Der Albert (using translation)
GeminiTranslationService (2.0): Name mapping - アルバート王国 → Albert Kingdom (using translation)
GeminiTranslationService (2.0): Name mapping - 第一王子 → First Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 王家の“影” → Royal "Shadows" (using translation)
GeminiTranslationService (2.0): Name mapping - 製法 → Manufacturing Method (using translation)
GeminiTranslationService (2.0): Name mapping - ルイス → Louis (using translation)
GeminiTranslationService (2.0): Name mapping - 映写魔道具 → Projection Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 辺境の村 → Frontier Village (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト → Luruto (using translation)
GeminiTranslationService (2.0): Name mapping - 地図 → Map (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョン → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 木剣 → Wooden Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワー → Charisma (using translation)
GeminiTranslationService (2.0): Name mapping - 空間ごと隔絶 → separated space (using translation)
GeminiTranslationService (2.0): Name mapping - 闇魔法 → Dark Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワ → Charisma Power (using translation)
GeminiTranslationService (2.0): Name mapping - コボルト → Kobold (using translation)
GeminiTranslationService (2.0): Name mapping - 王子殿下 → Your Royal Highness / Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 攻略対象 → Capture Target (using translation)
GeminiTranslationService (2.0): Name mapping - チートキャラ → Cheat Character (using translation)
GeminiTranslationService (2.0): Name mapping - ゴブリン → Goblin (using translation)
GeminiTranslationService (2.0): Name mapping - ホブゴブリン → Hobgoblin (using translation)
GeminiTranslationService (2.0): Name mapping - 棍棒 → Club (using translation)
GeminiTranslationService (2.0): Name mapping - 幻の剣 → Phantom Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 宮廷剣術 → Court Swordsmanship (using translation)
GeminiTranslationService (2.0): Name mapping - マジェスター公爵家 → Majester Ducal House (using translation)
GeminiTranslationService (2.0): Name mapping - オープニングイベント → Opening Event (using translation)
GeminiTranslationService (2.0): Name mapping - 炎の剣 → Sword of Flame (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵 → baron (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜 → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - お父様 → Otou-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お母様 → Okaa-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お姉さん → Onee-san (using translation)
GeminiTranslationService (2.0): Name mapping - おじさん → Ojisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばさん → Obasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいさん → Ojiisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあさん → Obaasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいちゃん → Ojiichan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあちゃん → Obaachan (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家の屋敷 → marquis&#039;s estate (using translation)
GeminiTranslationService (2.0): Name mapping - ドゴラ → Dogora (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商 → Slave Merchant (using translation)
GeminiTranslationService (2.0): Name mapping - 呪具 → Cursed Item (using translation)
GeminiTranslationService (2.0): Name mapping - 魔人 → Demon (using translation)
GeminiTranslationService (2.0): Name mapping - 闇天太陽 → Dark Sky Sun (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫、空閃 → Tenki, Kusen (Sky Princess, Sky Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - ワルガー → Walgar (using translation)
GeminiTranslationService (2.0): Name mapping - ハルトマン侯爵家 → Hartmann Marquisate Family (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア・ハルトマン → Shirlia Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ・レンジャー → Tilty Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド・レンジャー → Soldo Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 千里眼 → Clairvoyance (using translation)
Gemini20TranslationService: Attempt 1 of 3
Gemini20TranslationService: Using maxOutputTokens: 5120
Gemini20TranslationService: Non-retryable error, stopping retries
TranslationService: Provider failed, trying legacy Gemini fallback: HTTP error: 400
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Using maxOutputTokens: 8192
TranslationService: Gemini finishReason: STOP
TranslationService: API indicated completion with finish reason: STOP, skipping heuristic checks
TranslationService: Gemini language validation passed - detected english
TranslationService: Gemini dialogue tag issues detected: Significant change in dialogue quotation mark count: 634 → 44
TranslationService: Applied Gemini dialogue tag cleanup
TranslationService: Applying Gemini name substitutions with 182 dictionary entries
TranslationService: Applied 1 Gemini name substitutions
NameSubstitutionService: Applied 1 name substitutions in Gemini fallback translation
  - 魔法 → Magic (found variations: magic, replacements: 2)
TranslationService: Using AI Provider Manager with active provider: gemini_20
AIProviderManager: Using primary provider: gemini_20
GeminiTranslationService (2.0): Using name dictionary with 182 entries
GeminiTranslationService (2.0): Name mapping - 父さん → Tou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (2.0): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 母さん → Kaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お姉ちゃん → Onee-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お兄さん → Onii-san (using translation)
GeminiTranslationService (2.0): Name mapping - 剣聖 → Sword Saint (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力砲 → magic cannon (using translation)
GeminiTranslationService (2.0): Name mapping - 影 → Shadow (using translation)
GeminiTranslationService (2.0): Name mapping - タイタンズ → Titans (using translation)
GeminiTranslationService (2.0): Name mapping - 魔道具 → Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラゴン → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 回復アイテム → Healing Item (using translation)
GeminiTranslationService (2.0): Name mapping - アイン → Ain (using translation)
GeminiTranslationService (2.0): Name mapping - ライク → Laik (using translation)
GeminiTranslationService (2.0): Name mapping - 氷狼一閃 → Hyoro Issen (Ice Wolf Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流 → Majin Ryu (Demon God Style) (using translation)
GeminiTranslationService (2.0): Name mapping - 剣術 → Kenjutsu (Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法スキル → Magic Skill (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣 → Magic Beast (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流剣術 → Majin Ryu Kenjutsu (Demon God Style Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜炎斬 → Kokuryu Enzan (Black Dragon Flame Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョン → Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣災害 → Magic Beast Disaster (using translation)
GeminiTranslationService (2.0): Name mapping - 炎のブレス → Flame Breath (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼……一閃》 → 《Hyoro…Issen (Ice Wolf…Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼一閃》 → 《Hyoro Issen (Ice Wolf Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《黒竜炎斬》 → 《Kokuryu Enzan (Black Dragon Flame Slash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 紅蓮の炎 → Crimson Flame (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ → Tilty (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド → Soldo (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア → Shirlia (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家 → Marquis Household (using translation)
GeminiTranslationService (2.0): Name mapping - 《千里眼》 → Senrigan (Clairvoyance) (using translation)
GeminiTranslationService (2.0): Name mapping - ドラゴン → Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法 → Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力 → Magical Power (using translation)
GeminiTranslationService (2.0): Name mapping - 中立的な家臣達 → Neutral Retainers (using translation)
GeminiTranslationService (2.0): Name mapping - 対毒魔法 → Anti-Poison Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 妹達 → Imoto-tachi (using translation)
GeminiTranslationService (2.0): Name mapping - 次期当主 → Next Family Head (using translation)
GeminiTranslationService (2.0): Name mapping - シルリアさん → Shirlia-san (using translation)
GeminiTranslationService (2.0): Name mapping - 睡眠薬 → Sleeping pill (using translation)
GeminiTranslationService (2.0): Name mapping - ポヤポヤ → Fuzzy / Dazed / Absent-minded (using translation)
GeminiTranslationService (2.0): Name mapping - ホッ → Relief (sigh of relief) (using translation)
GeminiTranslationService (2.0): Name mapping - ゾッコン → Deeply in love / Infatuated (using translation)
GeminiTranslationService (2.0): Name mapping - メイド → Maid (using translation)
GeminiTranslationService (2.0): Name mapping - 心底ホッ → Deep Relief (using translation)
GeminiTranslationService (2.0): Name mapping - 腹黒メガネ → Scheming Glasses-wearer (using translation)
GeminiTranslationService (2.0): Name mapping - アインさん → Ain-san (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラ → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - アイテム → Item (using translation)
GeminiTranslationService (2.0): Name mapping - ダメージ → Damage (using translation)
GeminiTranslationService (2.0): Name mapping - リカバリー → Recovery (using translation)
GeminiTranslationService (2.0): Name mapping - プレイヤー → Player (using translation)
GeminiTranslationService (2.0): Name mapping - 炎球 → Fireball (using translation)
GeminiTranslationService (2.0): Name mapping - リスク → Risk (using translation)
GeminiTranslationService (2.0): Name mapping - ラゴン → ragon (using translation)
GeminiTranslationService (2.0): Name mapping - ライク様 → Laik-sama (using translation)
GeminiTranslationService (2.0): Name mapping - レンジャー領 → Ranger Territory (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士 → Knight (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商人 → Slave Trader (using translation)
GeminiTranslationService (2.0): Name mapping - ガストの町 → Gast Town (using translation)
GeminiTranslationService (2.0): Name mapping - 医務室 → Infirmary (using translation)
GeminiTranslationService (2.0): Name mapping - 支配の呪具 → Dominance Curse Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士団 → Knight Order (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵令嬢 → marquis’ daughter (using translation)
GeminiTranslationService (2.0): Name mapping - 闇巨人 → Dark Giant (using translation)
GeminiTranslationService (2.0): Name mapping - ガランド・レンジャー → Garand Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 王都 → Royal Capital (using translation)
GeminiTranslationService (2.0): Name mapping - 門 → Gate (using translation)
GeminiTranslationService (2.0): Name mapping - 二の型 → Second Form (using translation)
GeminiTranslationService (2.0): Name mapping - ライク・ハルトマン → Laik Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - 三の型 → Third Form (using translation)
GeminiTranslationService (2.0): Name mapping - ミラウ → Mirau (using translation)
GeminiTranslationService (2.0): Name mapping - フレイ → Frey (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫空閃 → Tenki Kusen (Celestial Princess Sky Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - 空間転移 → Spatial Transfer (using translation)
GeminiTranslationService (2.0): Name mapping - 剣士 → Swordsman (using translation)
GeminiTranslationService (2.0): Name mapping - 運び屋 → Carrier (using translation)
GeminiTranslationService (2.0): Name mapping - 訓練場 → Training Grounds (using translation)
GeminiTranslationService (2.0): Name mapping - パパ → Dad (using translation)
GeminiTranslationService (2.0): Name mapping - 氷華 → Ice Flower (using translation)
GeminiTranslationService (2.0): Name mapping - 師匠 → shisho (using translation)
GeminiTranslationService (2.0): Name mapping - 飛行 → Flight (using translation)
GeminiTranslationService (2.0): Name mapping - 風刃 → Wind Blade (using translation)
GeminiTranslationService (2.0): Name mapping - 好感度 → favorability (using translation)
GeminiTranslationService (2.0): Name mapping - 二刀流 → Dual-wielding (using translation)
GeminiTranslationService (2.0): Name mapping - 四の型 → Fourth Form (using translation)
GeminiTranslationService (2.0): Name mapping - 氷炎万華 → Hyoen Mange (Ice Flame Myriad Flowers) (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵家 → Baron's family (using translation)
GeminiTranslationService (2.0): Name mapping - 公爵家 → Duke's family (using translation)
GeminiTranslationService (2.0): Name mapping - 執事 → Butler (using translation)
GeminiTranslationService (2.0): Name mapping - テラード・マジェスター → Terrard Majester (using translation)
GeminiTranslationService (2.0): Name mapping - 闇組織 → Dark Organization (using translation)
GeminiTranslationService (2.0): Name mapping - 世界征服 → World Domination (using translation)
GeminiTranslationService (2.0): Name mapping - 従者 → Retainer (using translation)
GeminiTranslationService (2.0): Name mapping - 爵位 → Peerage/Title of Nobility (using translation)
GeminiTranslationService (2.0): Name mapping - 家格 → Rank (using translation)
GeminiTranslationService (2.0): Name mapping - 大道芸人 → Street Performer (using translation)
GeminiTranslationService (2.0): Name mapping - 王家 → Royal Family (using translation)
GeminiTranslationService (2.0): Name mapping - 学園 → Academy (using translation)
GeminiTranslationService (2.0): Name mapping - 魔石 → Magic Stone (using translation)
GeminiTranslationService (2.0): Name mapping - クラーク・ハルトマン → Clark Hartman (using translation)
GeminiTranslationService (2.0): Name mapping - 伯爵位 → Earldom (using translation)
GeminiTranslationService (2.0): Name mapping - 準貴族 → Quasi-Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 木っ端貴族 → Minor Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 刀 → Katana (using translation)
GeminiTranslationService (2.0): Name mapping - 応接室 → Reception Room (using translation)
GeminiTranslationService (2.0): Name mapping - 芸者 → Geisha (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト村 → Luruto Village (using translation)
GeminiTranslationService (2.0): Name mapping - 村長 → Village Chief (using translation)
GeminiTranslationService (2.0): Name mapping - コルス → Kols (using translation)
GeminiTranslationService (2.0): Name mapping - スフィア → Sufia (using translation)
GeminiTranslationService (2.0): Name mapping - ノブファンプレイヤー → Nobu Fan Player (using translation)
GeminiTranslationService (2.0): Name mapping - 聖属性 → Holy Attribute (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法学園 → Magic Academy (using translation)
GeminiTranslationService (2.0): Name mapping - クヴァト → Kuvato (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョンボス → Dungeon Boss (using translation)
GeminiTranslationService (2.0): Name mapping - 一の型 → First Form (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョ → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 獣タイプ → Beast Type (using translation)
GeminiTranslationService (2.0): Name mapping - 幽霊 → Ghost (using translation)
GeminiTranslationService (2.0): Name mapping - 剣舞 → Sword Dance (using translation)
GeminiTranslationService (2.0): Name mapping - 幽剣乱舞 → Yūken Ranbu (Phantom Sword Dance) (using translation)
GeminiTranslationService (2.0): Name mapping - ジャグリング → Juggling (using translation)
GeminiTranslationService (2.0): Name mapping - 村人 → Villager (using translation)
GeminiTranslationService (2.0): Name mapping - 五の型 → Fifth Form (using translation)
GeminiTranslationService (2.0): Name mapping - クロウ・デア・アルバート → Crow Der Albert (using translation)
GeminiTranslationService (2.0): Name mapping - アルバート王国 → Albert Kingdom (using translation)
GeminiTranslationService (2.0): Name mapping - 第一王子 → First Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 王家の“影” → Royal "Shadows" (using translation)
GeminiTranslationService (2.0): Name mapping - 製法 → Manufacturing Method (using translation)
GeminiTranslationService (2.0): Name mapping - ルイス → Louis (using translation)
GeminiTranslationService (2.0): Name mapping - 映写魔道具 → Projection Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 辺境の村 → Frontier Village (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト → Luruto (using translation)
GeminiTranslationService (2.0): Name mapping - 地図 → Map (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョン → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 木剣 → Wooden Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワー → Charisma (using translation)
GeminiTranslationService (2.0): Name mapping - 空間ごと隔絶 → separated space (using translation)
GeminiTranslationService (2.0): Name mapping - 闇魔法 → Dark Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワ → Charisma Power (using translation)
GeminiTranslationService (2.0): Name mapping - コボルト → Kobold (using translation)
GeminiTranslationService (2.0): Name mapping - 王子殿下 → Your Royal Highness / Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 攻略対象 → Capture Target (using translation)
GeminiTranslationService (2.0): Name mapping - チートキャラ → Cheat Character (using translation)
GeminiTranslationService (2.0): Name mapping - ゴブリン → Goblin (using translation)
GeminiTranslationService (2.0): Name mapping - ホブゴブリン → Hobgoblin (using translation)
GeminiTranslationService (2.0): Name mapping - 棍棒 → Club (using translation)
GeminiTranslationService (2.0): Name mapping - 幻の剣 → Phantom Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 宮廷剣術 → Court Swordsmanship (using translation)
GeminiTranslationService (2.0): Name mapping - マジェスター公爵家 → Majester Ducal House (using translation)
GeminiTranslationService (2.0): Name mapping - オープニングイベント → Opening Event (using translation)
GeminiTranslationService (2.0): Name mapping - 炎の剣 → Sword of Flame (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵 → baron (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜 → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - お父様 → Otou-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お母様 → Okaa-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お姉さん → Onee-san (using translation)
GeminiTranslationService (2.0): Name mapping - おじさん → Ojisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばさん → Obasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいさん → Ojiisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあさん → Obaasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいちゃん → Ojiichan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあちゃん → Obaachan (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家の屋敷 → marquis&#039;s estate (using translation)
GeminiTranslationService (2.0): Name mapping - ドゴラ → Dogora (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商 → Slave Merchant (using translation)
GeminiTranslationService (2.0): Name mapping - 呪具 → Cursed Item (using translation)
GeminiTranslationService (2.0): Name mapping - 魔人 → Demon (using translation)
GeminiTranslationService (2.0): Name mapping - 闇天太陽 → Dark Sky Sun (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫、空閃 → Tenki, Kusen (Sky Princess, Sky Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - ワルガー → Walgar (using translation)
GeminiTranslationService (2.0): Name mapping - ハルトマン侯爵家 → Hartmann Marquisate Family (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア・ハルトマン → Shirlia Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ・レンジャー → Tilty Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド・レンジャー → Soldo Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 千里眼 → Clairvoyance (using translation)
Gemini20TranslationService: Attempt 1 of 3
Gemini20TranslationService: Using maxOutputTokens: 5120
Gemini20TranslationService: Non-retryable error, stopping retries
TranslationService: Provider failed, trying legacy Gemini fallback: HTTP error: 400
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Using maxOutputTokens: 8192
TranslationService: Gemini finishReason: STOP
TranslationService: API indicated completion with finish reason: STOP, skipping heuristic checks
TranslationService: Gemini language validation passed - detected english
TranslationService: Gemini dialogue tag issues detected: Significant change in dialogue quotation mark count: 658 → 35, Dialogue line structure significantly changed: 19 → 12 lines with dialogue
TranslationService: Applied Gemini dialogue tag cleanup
TranslationService: Applying Gemini name substitutions with 182 dictionary entries
TranslationService: No Gemini name substitutions applied
TranslationService: Using AI Provider Manager with active provider: gemini_20
AIProviderManager: Using primary provider: gemini_20
GeminiTranslationService (2.0): Using name dictionary with 182 entries
GeminiTranslationService (2.0): Name mapping - 父さん → Tou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (2.0): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 母さん → Kaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お姉ちゃん → Onee-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お兄さん → Onii-san (using translation)
GeminiTranslationService (2.0): Name mapping - 剣聖 → Sword Saint (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力砲 → magic cannon (using translation)
GeminiTranslationService (2.0): Name mapping - 影 → Shadow (using translation)
GeminiTranslationService (2.0): Name mapping - タイタンズ → Titans (using translation)
GeminiTranslationService (2.0): Name mapping - 魔道具 → Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラゴン → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 回復アイテム → Healing Item (using translation)
GeminiTranslationService (2.0): Name mapping - アイン → Ain (using translation)
GeminiTranslationService (2.0): Name mapping - ライク → Laik (using translation)
GeminiTranslationService (2.0): Name mapping - 氷狼一閃 → Hyoro Issen (Ice Wolf Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流 → Majin Ryu (Demon God Style) (using translation)
GeminiTranslationService (2.0): Name mapping - 剣術 → Kenjutsu (Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法スキル → Magic Skill (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣 → Magic Beast (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流剣術 → Majin Ryu Kenjutsu (Demon God Style Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜炎斬 → Kokuryu Enzan (Black Dragon Flame Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョン → Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣災害 → Magic Beast Disaster (using translation)
GeminiTranslationService (2.0): Name mapping - 炎のブレス → Flame Breath (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼……一閃》 → 《Hyoro…Issen (Ice Wolf…Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼一閃》 → 《Hyoro Issen (Ice Wolf Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《黒竜炎斬》 → 《Kokuryu Enzan (Black Dragon Flame Slash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 紅蓮の炎 → Crimson Flame (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ → Tilty (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド → Soldo (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア → Shirlia (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家 → Marquis Household (using translation)
GeminiTranslationService (2.0): Name mapping - 《千里眼》 → Senrigan (Clairvoyance) (using translation)
GeminiTranslationService (2.0): Name mapping - ドラゴン → Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法 → Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力 → Magical Power (using translation)
GeminiTranslationService (2.0): Name mapping - 中立的な家臣達 → Neutral Retainers (using translation)
GeminiTranslationService (2.0): Name mapping - 対毒魔法 → Anti-Poison Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 妹達 → Imoto-tachi (using translation)
GeminiTranslationService (2.0): Name mapping - 次期当主 → Next Family Head (using translation)
GeminiTranslationService (2.0): Name mapping - シルリアさん → Shirlia-san (using translation)
GeminiTranslationService (2.0): Name mapping - 睡眠薬 → Sleeping pill (using translation)
GeminiTranslationService (2.0): Name mapping - ポヤポヤ → Fuzzy / Dazed / Absent-minded (using translation)
GeminiTranslationService (2.0): Name mapping - ホッ → Relief (sigh of relief) (using translation)
GeminiTranslationService (2.0): Name mapping - ゾッコン → Deeply in love / Infatuated (using translation)
GeminiTranslationService (2.0): Name mapping - メイド → Maid (using translation)
GeminiTranslationService (2.0): Name mapping - 心底ホッ → Deep Relief (using translation)
GeminiTranslationService (2.0): Name mapping - 腹黒メガネ → Scheming Glasses-wearer (using translation)
GeminiTranslationService (2.0): Name mapping - アインさん → Ain-san (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラ → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - アイテム → Item (using translation)
GeminiTranslationService (2.0): Name mapping - ダメージ → Damage (using translation)
GeminiTranslationService (2.0): Name mapping - リカバリー → Recovery (using translation)
GeminiTranslationService (2.0): Name mapping - プレイヤー → Player (using translation)
GeminiTranslationService (2.0): Name mapping - 炎球 → Fireball (using translation)
GeminiTranslationService (2.0): Name mapping - リスク → Risk (using translation)
GeminiTranslationService (2.0): Name mapping - ラゴン → ragon (using translation)
GeminiTranslationService (2.0): Name mapping - ライク様 → Laik-sama (using translation)
GeminiTranslationService (2.0): Name mapping - レンジャー領 → Ranger Territory (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士 → Knight (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商人 → Slave Trader (using translation)
GeminiTranslationService (2.0): Name mapping - ガストの町 → Gast Town (using translation)
GeminiTranslationService (2.0): Name mapping - 医務室 → Infirmary (using translation)
GeminiTranslationService (2.0): Name mapping - 支配の呪具 → Dominance Curse Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士団 → Knight Order (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵令嬢 → marquis’ daughter (using translation)
GeminiTranslationService (2.0): Name mapping - 闇巨人 → Dark Giant (using translation)
GeminiTranslationService (2.0): Name mapping - ガランド・レンジャー → Garand Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 王都 → Royal Capital (using translation)
GeminiTranslationService (2.0): Name mapping - 門 → Gate (using translation)
GeminiTranslationService (2.0): Name mapping - 二の型 → Second Form (using translation)
GeminiTranslationService (2.0): Name mapping - ライク・ハルトマン → Laik Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - 三の型 → Third Form (using translation)
GeminiTranslationService (2.0): Name mapping - ミラウ → Mirau (using translation)
GeminiTranslationService (2.0): Name mapping - フレイ → Frey (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫空閃 → Tenki Kusen (Celestial Princess Sky Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - 空間転移 → Spatial Transfer (using translation)
GeminiTranslationService (2.0): Name mapping - 剣士 → Swordsman (using translation)
GeminiTranslationService (2.0): Name mapping - 運び屋 → Carrier (using translation)
GeminiTranslationService (2.0): Name mapping - 訓練場 → Training Grounds (using translation)
GeminiTranslationService (2.0): Name mapping - パパ → Dad (using translation)
GeminiTranslationService (2.0): Name mapping - 氷華 → Ice Flower (using translation)
GeminiTranslationService (2.0): Name mapping - 師匠 → shisho (using translation)
GeminiTranslationService (2.0): Name mapping - 飛行 → Flight (using translation)
GeminiTranslationService (2.0): Name mapping - 風刃 → Wind Blade (using translation)
GeminiTranslationService (2.0): Name mapping - 好感度 → favorability (using translation)
GeminiTranslationService (2.0): Name mapping - 二刀流 → Dual-wielding (using translation)
GeminiTranslationService (2.0): Name mapping - 四の型 → Fourth Form (using translation)
GeminiTranslationService (2.0): Name mapping - 氷炎万華 → Hyoen Mange (Ice Flame Myriad Flowers) (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵家 → Baron's family (using translation)
GeminiTranslationService (2.0): Name mapping - 公爵家 → Duke's family (using translation)
GeminiTranslationService (2.0): Name mapping - 執事 → Butler (using translation)
GeminiTranslationService (2.0): Name mapping - テラード・マジェスター → Terrard Majester (using translation)
GeminiTranslationService (2.0): Name mapping - 闇組織 → Dark Organization (using translation)
GeminiTranslationService (2.0): Name mapping - 世界征服 → World Domination (using translation)
GeminiTranslationService (2.0): Name mapping - 従者 → Retainer (using translation)
GeminiTranslationService (2.0): Name mapping - 爵位 → Peerage/Title of Nobility (using translation)
GeminiTranslationService (2.0): Name mapping - 家格 → Rank (using translation)
GeminiTranslationService (2.0): Name mapping - 大道芸人 → Street Performer (using translation)
GeminiTranslationService (2.0): Name mapping - 王家 → Royal Family (using translation)
GeminiTranslationService (2.0): Name mapping - 学園 → Academy (using translation)
GeminiTranslationService (2.0): Name mapping - 魔石 → Magic Stone (using translation)
GeminiTranslationService (2.0): Name mapping - クラーク・ハルトマン → Clark Hartman (using translation)
GeminiTranslationService (2.0): Name mapping - 伯爵位 → Earldom (using translation)
GeminiTranslationService (2.0): Name mapping - 準貴族 → Quasi-Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 木っ端貴族 → Minor Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 刀 → Katana (using translation)
GeminiTranslationService (2.0): Name mapping - 応接室 → Reception Room (using translation)
GeminiTranslationService (2.0): Name mapping - 芸者 → Geisha (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト村 → Luruto Village (using translation)
GeminiTranslationService (2.0): Name mapping - 村長 → Village Chief (using translation)
GeminiTranslationService (2.0): Name mapping - コルス → Kols (using translation)
GeminiTranslationService (2.0): Name mapping - スフィア → Sufia (using translation)
GeminiTranslationService (2.0): Name mapping - ノブファンプレイヤー → Nobu Fan Player (using translation)
GeminiTranslationService (2.0): Name mapping - 聖属性 → Holy Attribute (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法学園 → Magic Academy (using translation)
GeminiTranslationService (2.0): Name mapping - クヴァト → Kuvato (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョンボス → Dungeon Boss (using translation)
GeminiTranslationService (2.0): Name mapping - 一の型 → First Form (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョ → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 獣タイプ → Beast Type (using translation)
GeminiTranslationService (2.0): Name mapping - 幽霊 → Ghost (using translation)
GeminiTranslationService (2.0): Name mapping - 剣舞 → Sword Dance (using translation)
GeminiTranslationService (2.0): Name mapping - 幽剣乱舞 → Yūken Ranbu (Phantom Sword Dance) (using translation)
GeminiTranslationService (2.0): Name mapping - ジャグリング → Juggling (using translation)
GeminiTranslationService (2.0): Name mapping - 村人 → Villager (using translation)
GeminiTranslationService (2.0): Name mapping - 五の型 → Fifth Form (using translation)
GeminiTranslationService (2.0): Name mapping - クロウ・デア・アルバート → Crow Der Albert (using translation)
GeminiTranslationService (2.0): Name mapping - アルバート王国 → Albert Kingdom (using translation)
GeminiTranslationService (2.0): Name mapping - 第一王子 → First Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 王家の“影” → Royal "Shadows" (using translation)
GeminiTranslationService (2.0): Name mapping - 製法 → Manufacturing Method (using translation)
GeminiTranslationService (2.0): Name mapping - ルイス → Louis (using translation)
GeminiTranslationService (2.0): Name mapping - 映写魔道具 → Projection Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 辺境の村 → Frontier Village (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト → Luruto (using translation)
GeminiTranslationService (2.0): Name mapping - 地図 → Map (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョン → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 木剣 → Wooden Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワー → Charisma (using translation)
GeminiTranslationService (2.0): Name mapping - 空間ごと隔絶 → separated space (using translation)
GeminiTranslationService (2.0): Name mapping - 闇魔法 → Dark Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワ → Charisma Power (using translation)
GeminiTranslationService (2.0): Name mapping - コボルト → Kobold (using translation)
GeminiTranslationService (2.0): Name mapping - 王子殿下 → Your Royal Highness / Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 攻略対象 → Capture Target (using translation)
GeminiTranslationService (2.0): Name mapping - チートキャラ → Cheat Character (using translation)
GeminiTranslationService (2.0): Name mapping - ゴブリン → Goblin (using translation)
GeminiTranslationService (2.0): Name mapping - ホブゴブリン → Hobgoblin (using translation)
GeminiTranslationService (2.0): Name mapping - 棍棒 → Club (using translation)
GeminiTranslationService (2.0): Name mapping - 幻の剣 → Phantom Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 宮廷剣術 → Court Swordsmanship (using translation)
GeminiTranslationService (2.0): Name mapping - マジェスター公爵家 → Majester Ducal House (using translation)
GeminiTranslationService (2.0): Name mapping - オープニングイベント → Opening Event (using translation)
GeminiTranslationService (2.0): Name mapping - 炎の剣 → Sword of Flame (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵 → baron (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜 → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - お父様 → Otou-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お母様 → Okaa-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お姉さん → Onee-san (using translation)
GeminiTranslationService (2.0): Name mapping - おじさん → Ojisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばさん → Obasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいさん → Ojiisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあさん → Obaasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいちゃん → Ojiichan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあちゃん → Obaachan (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家の屋敷 → marquis&#039;s estate (using translation)
GeminiTranslationService (2.0): Name mapping - ドゴラ → Dogora (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商 → Slave Merchant (using translation)
GeminiTranslationService (2.0): Name mapping - 呪具 → Cursed Item (using translation)
GeminiTranslationService (2.0): Name mapping - 魔人 → Demon (using translation)
GeminiTranslationService (2.0): Name mapping - 闇天太陽 → Dark Sky Sun (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫、空閃 → Tenki, Kusen (Sky Princess, Sky Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - ワルガー → Walgar (using translation)
GeminiTranslationService (2.0): Name mapping - ハルトマン侯爵家 → Hartmann Marquisate Family (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア・ハルトマン → Shirlia Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ・レンジャー → Tilty Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド・レンジャー → Soldo Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 千里眼 → Clairvoyance (using translation)
Gemini20TranslationService: Attempt 1 of 3
Gemini20TranslationService: Using maxOutputTokens: 5120
Gemini20TranslationService: Non-retryable error, stopping retries
TranslationService: Provider failed, trying legacy Gemini fallback: HTTP error: 400
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: Using maxOutputTokens: 8192
TranslationService: Gemini finishReason: STOP
TranslationService: API indicated completion with finish reason: STOP, skipping heuristic checks
TranslationService: Gemini language validation passed - detected english
TranslationService: Gemini dialogue tag issues detected: Significant change in dialogue quotation mark count: 638 → 35, Dialogue line structure significantly changed: 17 → 13 lines with dialogue
TranslationService: Applied Gemini dialogue tag cleanup
TranslationService: Applying Gemini name substitutions with 182 dictionary entries
TranslationService: No Gemini name substitutions applied
TranslationService: Using AI Provider Manager with active provider: gemini_20
AIProviderManager: Using primary provider: gemini_20
GeminiTranslationService (2.0): Using name dictionary with 182 entries
GeminiTranslationService (2.0): Name mapping - 父さん → Tou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 兄さん → Nii-san (using translation)
GeminiTranslationService (2.0): Name mapping - お父さん → Otou-san (using translation)
GeminiTranslationService (2.0): Name mapping - 母さん → Kaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お母さん → Okaa-san (using translation)
GeminiTranslationService (2.0): Name mapping - お兄ちゃん → Onii-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お姉ちゃん → Onee-chan (using translation)
GeminiTranslationService (2.0): Name mapping - お兄さん → Onii-san (using translation)
GeminiTranslationService (2.0): Name mapping - 剣聖 → Sword Saint (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力砲 → magic cannon (using translation)
GeminiTranslationService (2.0): Name mapping - 影 → Shadow (using translation)
GeminiTranslationService (2.0): Name mapping - タイタンズ → Titans (using translation)
GeminiTranslationService (2.0): Name mapping - 魔道具 → Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラゴン → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 回復アイテム → Healing Item (using translation)
GeminiTranslationService (2.0): Name mapping - アイン → Ain (using translation)
GeminiTranslationService (2.0): Name mapping - ライク → Laik (using translation)
GeminiTranslationService (2.0): Name mapping - 氷狼一閃 → Hyoro Issen (Ice Wolf Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流 → Majin Ryu (Demon God Style) (using translation)
GeminiTranslationService (2.0): Name mapping - 剣術 → Kenjutsu (Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法スキル → Magic Skill (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣 → Magic Beast (using translation)
GeminiTranslationService (2.0): Name mapping - 魔神流剣術 → Majin Ryu Kenjutsu (Demon God Style Swordsmanship) (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜炎斬 → Kokuryu Enzan (Black Dragon Flame Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョン → Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔獣災害 → Magic Beast Disaster (using translation)
GeminiTranslationService (2.0): Name mapping - 炎のブレス → Flame Breath (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼……一閃》 → 《Hyoro…Issen (Ice Wolf…Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《氷狼一閃》 → 《Hyoro Issen (Ice Wolf Flash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 《黒竜炎斬》 → 《Kokuryu Enzan (Black Dragon Flame Slash)》 (using translation)
GeminiTranslationService (2.0): Name mapping - 紅蓮の炎 → Crimson Flame (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ → Tilty (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド → Soldo (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア → Shirlia (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家 → Marquis Household (using translation)
GeminiTranslationService (2.0): Name mapping - 《千里眼》 → Senrigan (Clairvoyance) (using translation)
GeminiTranslationService (2.0): Name mapping - ドラゴン → Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法 → Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魔力 → Magical Power (using translation)
GeminiTranslationService (2.0): Name mapping - 中立的な家臣達 → Neutral Retainers (using translation)
GeminiTranslationService (2.0): Name mapping - 対毒魔法 → Anti-Poison Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 妹達 → Imoto-tachi (using translation)
GeminiTranslationService (2.0): Name mapping - 次期当主 → Next Family Head (using translation)
GeminiTranslationService (2.0): Name mapping - シルリアさん → Shirlia-san (using translation)
GeminiTranslationService (2.0): Name mapping - 睡眠薬 → Sleeping pill (using translation)
GeminiTranslationService (2.0): Name mapping - ポヤポヤ → Fuzzy / Dazed / Absent-minded (using translation)
GeminiTranslationService (2.0): Name mapping - ホッ → Relief (sigh of relief) (using translation)
GeminiTranslationService (2.0): Name mapping - ゾッコン → Deeply in love / Infatuated (using translation)
GeminiTranslationService (2.0): Name mapping - メイド → Maid (using translation)
GeminiTranslationService (2.0): Name mapping - 心底ホッ → Deep Relief (using translation)
GeminiTranslationService (2.0): Name mapping - 腹黒メガネ → Scheming Glasses-wearer (using translation)
GeminiTranslationService (2.0): Name mapping - アインさん → Ain-san (using translation)
GeminiTranslationService (2.0): Name mapping - ブラックドラ → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - アイテム → Item (using translation)
GeminiTranslationService (2.0): Name mapping - ダメージ → Damage (using translation)
GeminiTranslationService (2.0): Name mapping - リカバリー → Recovery (using translation)
GeminiTranslationService (2.0): Name mapping - プレイヤー → Player (using translation)
GeminiTranslationService (2.0): Name mapping - 炎球 → Fireball (using translation)
GeminiTranslationService (2.0): Name mapping - リスク → Risk (using translation)
GeminiTranslationService (2.0): Name mapping - ラゴン → ragon (using translation)
GeminiTranslationService (2.0): Name mapping - ライク様 → Laik-sama (using translation)
GeminiTranslationService (2.0): Name mapping - レンジャー領 → Ranger Territory (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士 → Knight (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商人 → Slave Trader (using translation)
GeminiTranslationService (2.0): Name mapping - ガストの町 → Gast Town (using translation)
GeminiTranslationService (2.0): Name mapping - 医務室 → Infirmary (using translation)
GeminiTranslationService (2.0): Name mapping - 支配の呪具 → Dominance Curse Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 騎士団 → Knight Order (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵令嬢 → marquis’ daughter (using translation)
GeminiTranslationService (2.0): Name mapping - 闇巨人 → Dark Giant (using translation)
GeminiTranslationService (2.0): Name mapping - ガランド・レンジャー → Garand Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 王都 → Royal Capital (using translation)
GeminiTranslationService (2.0): Name mapping - 門 → Gate (using translation)
GeminiTranslationService (2.0): Name mapping - 二の型 → Second Form (using translation)
GeminiTranslationService (2.0): Name mapping - ライク・ハルトマン → Laik Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - 三の型 → Third Form (using translation)
GeminiTranslationService (2.0): Name mapping - ミラウ → Mirau (using translation)
GeminiTranslationService (2.0): Name mapping - フレイ → Frey (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫空閃 → Tenki Kusen (Celestial Princess Sky Slash) (using translation)
GeminiTranslationService (2.0): Name mapping - 空間転移 → Spatial Transfer (using translation)
GeminiTranslationService (2.0): Name mapping - 剣士 → Swordsman (using translation)
GeminiTranslationService (2.0): Name mapping - 運び屋 → Carrier (using translation)
GeminiTranslationService (2.0): Name mapping - 訓練場 → Training Grounds (using translation)
GeminiTranslationService (2.0): Name mapping - パパ → Dad (using translation)
GeminiTranslationService (2.0): Name mapping - 氷華 → Ice Flower (using translation)
GeminiTranslationService (2.0): Name mapping - 師匠 → shisho (using translation)
GeminiTranslationService (2.0): Name mapping - 飛行 → Flight (using translation)
GeminiTranslationService (2.0): Name mapping - 風刃 → Wind Blade (using translation)
GeminiTranslationService (2.0): Name mapping - 好感度 → favorability (using translation)
GeminiTranslationService (2.0): Name mapping - 二刀流 → Dual-wielding (using translation)
GeminiTranslationService (2.0): Name mapping - 四の型 → Fourth Form (using translation)
GeminiTranslationService (2.0): Name mapping - 氷炎万華 → Hyoen Mange (Ice Flame Myriad Flowers) (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵家 → Baron's family (using translation)
GeminiTranslationService (2.0): Name mapping - 公爵家 → Duke's family (using translation)
GeminiTranslationService (2.0): Name mapping - 執事 → Butler (using translation)
GeminiTranslationService (2.0): Name mapping - テラード・マジェスター → Terrard Majester (using translation)
GeminiTranslationService (2.0): Name mapping - 闇組織 → Dark Organization (using translation)
GeminiTranslationService (2.0): Name mapping - 世界征服 → World Domination (using translation)
GeminiTranslationService (2.0): Name mapping - 従者 → Retainer (using translation)
GeminiTranslationService (2.0): Name mapping - 爵位 → Peerage/Title of Nobility (using translation)
GeminiTranslationService (2.0): Name mapping - 家格 → Rank (using translation)
GeminiTranslationService (2.0): Name mapping - 大道芸人 → Street Performer (using translation)
GeminiTranslationService (2.0): Name mapping - 王家 → Royal Family (using translation)
GeminiTranslationService (2.0): Name mapping - 学園 → Academy (using translation)
GeminiTranslationService (2.0): Name mapping - 魔石 → Magic Stone (using translation)
GeminiTranslationService (2.0): Name mapping - クラーク・ハルトマン → Clark Hartman (using translation)
GeminiTranslationService (2.0): Name mapping - 伯爵位 → Earldom (using translation)
GeminiTranslationService (2.0): Name mapping - 準貴族 → Quasi-Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 木っ端貴族 → Minor Noble (using translation)
GeminiTranslationService (2.0): Name mapping - 刀 → Katana (using translation)
GeminiTranslationService (2.0): Name mapping - 応接室 → Reception Room (using translation)
GeminiTranslationService (2.0): Name mapping - 芸者 → Geisha (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト村 → Luruto Village (using translation)
GeminiTranslationService (2.0): Name mapping - 村長 → Village Chief (using translation)
GeminiTranslationService (2.0): Name mapping - コルス → Kols (using translation)
GeminiTranslationService (2.0): Name mapping - スフィア → Sufia (using translation)
GeminiTranslationService (2.0): Name mapping - ノブファンプレイヤー → Nobu Fan Player (using translation)
GeminiTranslationService (2.0): Name mapping - 聖属性 → Holy Attribute (using translation)
GeminiTranslationService (2.0): Name mapping - 魔法学園 → Magic Academy (using translation)
GeminiTranslationService (2.0): Name mapping - クヴァト → Kuvato (using translation)
GeminiTranslationService (2.0): Name mapping - ダンジョンボス → Dungeon Boss (using translation)
GeminiTranslationService (2.0): Name mapping - 一の型 → First Form (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョ → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 獣タイプ → Beast Type (using translation)
GeminiTranslationService (2.0): Name mapping - 幽霊 → Ghost (using translation)
GeminiTranslationService (2.0): Name mapping - 剣舞 → Sword Dance (using translation)
GeminiTranslationService (2.0): Name mapping - 幽剣乱舞 → Yūken Ranbu (Phantom Sword Dance) (using translation)
GeminiTranslationService (2.0): Name mapping - ジャグリング → Juggling (using translation)
GeminiTranslationService (2.0): Name mapping - 村人 → Villager (using translation)
GeminiTranslationService (2.0): Name mapping - 五の型 → Fifth Form (using translation)
GeminiTranslationService (2.0): Name mapping - クロウ・デア・アルバート → Crow Der Albert (using translation)
GeminiTranslationService (2.0): Name mapping - アルバート王国 → Albert Kingdom (using translation)
GeminiTranslationService (2.0): Name mapping - 第一王子 → First Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 王家の“影” → Royal "Shadows" (using translation)
GeminiTranslationService (2.0): Name mapping - 製法 → Manufacturing Method (using translation)
GeminiTranslationService (2.0): Name mapping - ルイス → Louis (using translation)
GeminiTranslationService (2.0): Name mapping - 映写魔道具 → Projection Magic Tool (using translation)
GeminiTranslationService (2.0): Name mapping - 辺境の村 → Frontier Village (using translation)
GeminiTranslationService (2.0): Name mapping - ルルト → Luruto (using translation)
GeminiTranslationService (2.0): Name mapping - 地図 → Map (using translation)
GeminiTranslationService (2.0): Name mapping - 初ダンジョン → First Dungeon (using translation)
GeminiTranslationService (2.0): Name mapping - 木剣 → Wooden Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワー → Charisma (using translation)
GeminiTranslationService (2.0): Name mapping - 空間ごと隔絶 → separated space (using translation)
GeminiTranslationService (2.0): Name mapping - 闇魔法 → Dark Magic (using translation)
GeminiTranslationService (2.0): Name mapping - 魅了パワ → Charisma Power (using translation)
GeminiTranslationService (2.0): Name mapping - コボルト → Kobold (using translation)
GeminiTranslationService (2.0): Name mapping - 王子殿下 → Your Royal Highness / Prince (using translation)
GeminiTranslationService (2.0): Name mapping - 攻略対象 → Capture Target (using translation)
GeminiTranslationService (2.0): Name mapping - チートキャラ → Cheat Character (using translation)
GeminiTranslationService (2.0): Name mapping - ゴブリン → Goblin (using translation)
GeminiTranslationService (2.0): Name mapping - ホブゴブリン → Hobgoblin (using translation)
GeminiTranslationService (2.0): Name mapping - 棍棒 → Club (using translation)
GeminiTranslationService (2.0): Name mapping - 幻の剣 → Phantom Sword (using translation)
GeminiTranslationService (2.0): Name mapping - 宮廷剣術 → Court Swordsmanship (using translation)
GeminiTranslationService (2.0): Name mapping - マジェスター公爵家 → Majester Ducal House (using translation)
GeminiTranslationService (2.0): Name mapping - オープニングイベント → Opening Event (using translation)
GeminiTranslationService (2.0): Name mapping - 炎の剣 → Sword of Flame (using translation)
GeminiTranslationService (2.0): Name mapping - 男爵 → baron (using translation)
GeminiTranslationService (2.0): Name mapping - 黒竜 → Black Dragon (using translation)
GeminiTranslationService (2.0): Name mapping - お父様 → Otou-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お母様 → Okaa-sama (using translation)
GeminiTranslationService (2.0): Name mapping - お姉さん → Onee-san (using translation)
GeminiTranslationService (2.0): Name mapping - おじさん → Ojisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばさん → Obasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいさん → Ojiisan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあさん → Obaasan (using translation)
GeminiTranslationService (2.0): Name mapping - おじいちゃん → Ojiichan (using translation)
GeminiTranslationService (2.0): Name mapping - おばあちゃん → Obaachan (using translation)
GeminiTranslationService (2.0): Name mapping - 侯爵家の屋敷 → marquis&#039;s estate (using translation)
GeminiTranslationService (2.0): Name mapping - ドゴラ → Dogora (using translation)
GeminiTranslationService (2.0): Name mapping - 奴隷商 → Slave Merchant (using translation)
GeminiTranslationService (2.0): Name mapping - 呪具 → Cursed Item (using translation)
GeminiTranslationService (2.0): Name mapping - 魔人 → Demon (using translation)
GeminiTranslationService (2.0): Name mapping - 闇天太陽 → Dark Sky Sun (using translation)
GeminiTranslationService (2.0): Name mapping - 天姫、空閃 → Tenki, Kusen (Sky Princess, Sky Flash) (using translation)
GeminiTranslationService (2.0): Name mapping - ワルガー → Walgar (using translation)
GeminiTranslationService (2.0): Name mapping - ハルトマン侯爵家 → Hartmann Marquisate Family (using translation)
GeminiTranslationService (2.0): Name mapping - シルリア・ハルトマン → Shirlia Hartmann (using translation)
GeminiTranslationService (2.0): Name mapping - ティルティ・レンジャー → Tilty Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - ソルド・レンジャー → Soldo Ranger (using translation)
GeminiTranslationService (2.0): Name mapping - 千里眼 → Clairvoyance (using translation)
Gemini20TranslationService: Attempt 1 of 3
Gemini20TranslationService: Using maxOutputTokens: 5120
TranslationService: Translation successful with provider: gemini_20
TranslationService: Language validation passed - detected english
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Dialogue tag issues detected: Significant change in dialogue quotation mark count: 43 → 1
TranslationService: Applied dialogue tag cleanup
TranslationService: Applying name substitutions with 182 dictionary entries
TranslationService: No name substitutions applied
AIProviderManager: Active provider: gemini_20
AIProviderManager: Active provider: gemini_20
AIProviderManager: Active provider: gemini_20
AIProviderManager: Active provider: gemini_20
Gemini25TranslationService: Experimental attempt 1 of 4
Gemini25TranslationService: Using maxOutputTokens: 4096
AIProviderManager: Active provider: gemini_20
AIProviderManager: Active provider: gemini_20
AIProviderManager: Active provider changed to: gemini_25
AIProviderManager: Active provider: gemini_25
AIProviderManager: Active provider: gemini_25
AIProviderManager: Active provider: gemini_25
AIProviderManager: Active provider: gemini_25
AIProviderManager: Active provider changed to: gemini_20
AIProviderManager: Active provider: gemini_20
AIProviderManager: Active provider: gemini_20
AIProviderManager: Active provider changed to: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Using max_tokens: 6144
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Using max_tokens: 6144

=== FORCE 3RD PERSON DEBUG TEST ===
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Using max_tokens: 6144
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Using max_tokens: 6144
DeepSeekTranslationService: Non-retryable error, stopping retries
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
TranslationService: Using AI Provider Manager with active provider: deepseek
AIProviderManager: Using primary provider: deepseek
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Using max_tokens: 6144
TranslationService: Translation successful with provider: deepseek
TranslationService: Language validation passed - detected english
TranslationService: Formatting validation issues: Dialogue quotation marks were lost in translation, Significant punctuation loss detected
TranslationService: Dialogue tag issues detected: All dialogue quotation marks were lost in translation
TranslationService: Applied dialogue tag cleanup
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Using max_tokens: 6144
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Using max_tokens: 6144
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Using max_tokens: 6144
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Using max_tokens: 6144
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Using max_tokens: 6144
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
[2025-07-15 14:05:34] NameSubstitutionService: Conflict resolution for '田中太郎': SKIPPED | Reason: Lower priority than conflicting names
[2025-07-15 14:05:34] NameSubstitutionService: Conflict resolution for '田中': SKIPPED | Reason: Lower priority than conflicting names
[2025-07-15 14:05:34] NameSubstitutionService: Conflict resolution for 'アリス': SKIPPED | Reason: Lower priority than conflicting names
[2025-07-15 14:05:34] NameSubstitutionService: Name processing: 'アリサ' → 'Alisa' (character, freq: 8) → NO_MATCHES | Reason: No variations found in translated text
[2025-07-15 14:05:34] NameSubstitutionService: Name processing: '王様' → 'King-sama' (character, freq: 5) → SKIPPED | Reason: Original name not found in source text
[2025-07-15 14:05:34] NameSubstitutionService: Conflict resolution for '王': SKIPPED | Reason: Lower priority than conflicting names
[2025-07-15 14:05:34] NameSubstitutionService: Conflict resolution for 'サラ': SKIPPED | Reason: Lower priority than conflicting names
[2025-07-15 14:05:34] NameSubstitutionService: Conflict resolution for 'リンダ': SKIPPED | Reason: Lower priority than conflicting names
[2025-07-15 14:05:34] NameSubstitutionService: Name processing: 'リン' → 'Rin' (character, freq: 25) → NO_MATCHES | Reason: No variations found in translated text
[2025-07-15 14:05:50] NameSubstitutionService: Conflict resolution for 'アレクサンダー': SKIPPED | Reason: Lower priority than conflicting names
[2025-07-15 14:05:50] NameSubstitutionService: Conflict resolution for 'アレックス': SKIPPED | Reason: Lower priority than conflicting names
[2025-07-15 14:05:50] NameSubstitutionService: Name processing: 'アレクサ' → 'Alexa' (character, freq: 8) → NO_MATCHES | Reason: No variations found in translated text
[2025-07-15 14:05:50] NameSubstitutionService: Conflict resolution for '山田太郎': SKIPPED | Reason: Lower priority than conflicting names
[2025-07-15 14:05:50] NameSubstitutionService: Conflict resolution for '山田花子': SKIPPED | Reason: Lower priority than conflicting names
[2025-07-15 14:05:50] NameSubstitutionService: Conflict resolution for '山田': SKIPPED | Reason: Lower priority than conflicting names
[2025-07-15 14:05:50] NameSubstitutionService: Conflict resolution for 'ケント': SKIPPED | Reason: Lower priority than conflicting names
[2025-07-15 14:05:50] NameSubstitutionService: Name processing: 'ケン' → 'Ken' (character, freq: 50) → NO_MATCHES | Reason: No variations found in translated text
[2025-07-15 14:05:50] NameSubstitutionService: Conflict resolution for '彼女': SKIPPED | Reason: Lower priority than conflicting names
[2025-07-15 14:05:50] NameSubstitutionService: Conflict resolution for '彼': SKIPPED | Reason: Lower priority than conflicting names
[2025-07-15 14:05:50] NameSubstitutionService: Conflict resolution for 'マリア': SKIPPED | Reason: Lower priority than conflicting names
[2025-07-15 14:07:13] NameSubstitutionService: Name processing: 'John' → 'John-kun' (character, freq: 10) → NO_MATCHES | Reason: No variations found in translated text
[2025-07-15 14:07:13] NameSubstitutionService: Name processing: 'Mary' → 'Mary-chan' (character, freq: 8) → NO_MATCHES | Reason: No variations found in translated text
[2025-07-15 14:07:13] NameSubstitutionService: Name processing: 'Alexander' → 'Alexander-sama' (character, freq: 15) → NO_MATCHES | Reason: No variations found in translated text
[2025-07-15 14:07:13] NameSubstitutionService: Name processing: 'Alex' → 'Alex-kun' (character, freq: 20) → NO_MATCHES | Reason: No variations found in translated text
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
AIProviderManager: Active provider: deepseek
