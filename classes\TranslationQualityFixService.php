<?php
/**
 * Translation Quality Fix Service
 * Applies automatic corrections to translated content based on quality check results
 */

class TranslationQualityFixService {
    private $db;
    private $nameSubstitutionService;
    private $automaticFormattingService;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->nameSubstitutionService = new NameSubstitutionService();
        $this->automaticFormattingService = new AutomaticFormattingService();
    }
    
    /**
     * Apply corrections to chapter translation
     */
    public function applyCorrections(int $novelId, int $chapterNumber, array $fixTypes, array $corrections): array {
        $startTime = microtime(true);
        
        try {
            // Get current chapter data
            $chapter = $this->getChapterData($novelId, $chapterNumber);
            if (!$chapter) {
                return [
                    'success' => false,
                    'error' => 'Chapter not found or has no translation'
                ];
            }
            
            $originalContent = $chapter['translated_content'];
            $updatedContent = $originalContent;
            $appliedFixes = [];
            
            // Apply corrections based on fix types
            foreach ($fixTypes as $fixType) {
                switch ($fixType) {
                    case 'name_dictionary':
                        $result = $this->applyNameCorrections($updatedContent, $corrections['name_dictionary'] ?? []);
                        $updatedContent = $result['content'];
                        $appliedFixes = array_merge($appliedFixes, $result['fixes']);
                        break;
                        
                    case 'formatting':
                        $result = $this->applyFormattingCorrections($chapter['original_content'], $updatedContent, $corrections['formatting'] ?? []);
                        $updatedContent = $result['content'];
                        $appliedFixes = array_merge($appliedFixes, $result['fixes']);
                        break;
                        
                    case 'punctuation':
                        $result = $this->applyPunctuationCorrections($updatedContent, $corrections['punctuation'] ?? []);
                        $updatedContent = $result['content'];
                        $appliedFixes = array_merge($appliedFixes, $result['fixes']);
                        break;
                }
            }
            
            // Update chapter in database if changes were made
            if ($updatedContent !== $originalContent) {
                $this->updateChapterTranslation($chapter['id'], $updatedContent);
                
                // Log the correction
                $this->logQualityCorrection($novelId, $chapterNumber, $appliedFixes);
            }
            
            return [
                'success' => true,
                'fixes_applied' => count($appliedFixes),
                'updated_content' => $updatedContent,
                'summary' => $this->generateFixSummary($appliedFixes),
                'execution_time' => round(microtime(true) - $startTime, 2)
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to apply corrections: ' . $e->getMessage(),
                'execution_time' => round(microtime(true) - $startTime, 2)
            ];
        }
    }
    
    /**
     * Preview corrections without applying them
     */
    public function previewCorrections(int $novelId, int $chapterNumber, array $corrections): array {
        $chapter = $this->getChapterData($novelId, $chapterNumber);
        if (!$chapter) {
            throw new Exception('Chapter not found');
        }
        
        $preview = [];
        $content = $chapter['translated_content'];
        
        // Preview name corrections
        if (isset($corrections['name_dictionary'])) {
            foreach ($corrections['name_dictionary'] as $correction) {
                $preview[] = [
                    'type' => 'name_dictionary',
                    'description' => $correction['description'],
                    'before' => $this->extractContext($content, $correction['from']),
                    'after' => str_replace($correction['from'], $correction['to'], $this->extractContext($content, $correction['from'])),
                    'confidence' => $correction['confidence']
                ];
            }
        }
        
        // Preview formatting corrections
        if (isset($corrections['formatting'])) {
            foreach ($corrections['formatting'] as $correction) {
                $preview[] = [
                    'type' => 'formatting',
                    'description' => $correction['description'],
                    'action' => $correction['action'],
                    'confidence' => $correction['confidence']
                ];
            }
        }
        
        // Preview punctuation corrections
        if (isset($corrections['punctuation'])) {
            foreach ($corrections['punctuation'] as $correction) {
                $preview[] = [
                    'type' => 'punctuation',
                    'description' => $correction['description'],
                    'action' => $correction['action'],
                    'confidence' => $correction['confidence']
                ];
            }
        }
        
        return $preview;
    }
    
    /**
     * Get chapter data
     */
    private function getChapterData(int $novelId, int $chapterNumber): ?array {
        return $this->db->fetchOne(
            "SELECT id, original_content, translated_content, translation_status 
             FROM chapters 
             WHERE novel_id = ? AND chapter_number = ?",
            [$novelId, $chapterNumber]
        );
    }
    
    /**
     * Apply name dictionary corrections
     */
    private function applyNameCorrections(string $content, array $corrections): array {
        $appliedFixes = [];
        
        foreach ($corrections as $correction) {
            if ($correction['type'] === 'name_replacement' || $correction['type'] === 'name_translation') {
                $before = $content;
                $content = str_replace($correction['from'], $correction['to'], $content);
                
                if ($content !== $before) {
                    $appliedFixes[] = [
                        'type' => 'name_correction',
                        'description' => $correction['description'],
                        'from' => $correction['from'],
                        'to' => $correction['to']
                    ];
                }
            }
        }
        
        return [
            'content' => $content,
            'fixes' => $appliedFixes
        ];
    }
    
    /**
     * Apply formatting corrections
     */
    private function applyFormattingCorrections(string $originalContent, string $translatedContent, array $corrections): array {
        $appliedFixes = [];
        $content = $translatedContent;
        
        foreach ($corrections as $correction) {
            switch ($correction['action']) {
                case 'restore_line_breaks':
                    $result = $this->automaticFormattingService->validateAndEnforceFormatting($originalContent, $content);
                    if ($result['was_restored']) {
                        $content = $result['text'];
                        $appliedFixes[] = [
                            'type' => 'formatting',
                            'description' => 'Restored line breaks to match original structure'
                        ];
                    }
                    break;
                    
                case 'restore_paragraphs':
                    $content = $this->restoreParagraphStructure($originalContent, $content);
                    $appliedFixes[] = [
                        'type' => 'formatting',
                        'description' => 'Restored paragraph structure'
                    ];
                    break;
            }
        }
        
        return [
            'content' => $content,
            'fixes' => $appliedFixes
        ];
    }
    
    /**
     * Apply punctuation corrections
     */
    private function applyPunctuationCorrections(string $content, array $corrections): array {
        $appliedFixes = [];

        // Always apply comprehensive punctuation fixes
        $originalContent = $content;

        // Fix Japanese punctuation issues first
        $content = $this->fixJapanesePunctuationIssues($content);
        if ($content !== $originalContent) {
            $appliedFixes[] = [
                'type' => 'punctuation',
                'description' => 'Fixed Japanese punctuation marks in English text'
            ];
        }

        // Apply specific corrections from quality check
        foreach ($corrections as $correction) {
            switch ($correction['action']) {
                case 'fix_japanese_punctuation':
                    $before = $content;
                    $content = $this->fixJapanesePunctuationIssues($content);
                    if ($content !== $before) {
                        $appliedFixes[] = [
                            'type' => 'punctuation',
                            'description' => 'Fixed Japanese punctuation marks in English text'
                        ];
                    }
                    break;

                case 'fix_quotes':
                    $before = $content;
                    $content = $this->fixUnmatchedQuotes($content);
                    if ($content !== $before) {
                        $appliedFixes[] = [
                            'type' => 'punctuation',
                            'description' => 'Fixed unmatched quotation marks'
                        ];
                    }
                    break;

                case 'fix_parentheses':
                    $before = $content;
                    $content = $this->fixUnmatchedParentheses($content);
                    if ($content !== $before) {
                        $appliedFixes[] = [
                            'type' => 'punctuation',
                            'description' => 'Fixed unmatched parentheses'
                        ];
                    }
                    break;
            }
        }

        return [
            'content' => $content,
            'fixes' => $appliedFixes
        ];
    }
    
    /**
     * Update chapter translation in database
     */
    private function updateChapterTranslation(int $chapterId, string $content): void {
        $this->db->update(
            'chapters',
            ['translated_content' => $content, 'updated_at' => date('Y-m-d H:i:s')],
            'id = ?',
            [$chapterId]
        );
    }
    
    /**
     * Log quality correction for audit trail (optional - won't fail if table doesn't exist)
     */
    private function logQualityCorrection(int $novelId, int $chapterNumber, array $fixes): void {
        try {
            $this->db->insert('quality_corrections_log', [
                'novel_id' => $novelId,
                'chapter_number' => $chapterNumber,
                'fixes_applied' => json_encode($fixes),
                'applied_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            // Log the error but don't fail the operation
            error_log("Quality correction logging failed: " . $e->getMessage());
        }
    }
    
    /**
     * Generate summary of applied fixes
     */
    private function generateFixSummary(array $fixes): array {
        $summary = [
            'total_fixes' => count($fixes),
            'by_type' => []
        ];
        
        foreach ($fixes as $fix) {
            $type = $fix['type'];
            if (!isset($summary['by_type'][$type])) {
                $summary['by_type'][$type] = 0;
            }
            $summary['by_type'][$type]++;
        }
        
        return $summary;
    }
    
    /**
     * Extract context around a specific text for preview
     */
    private function extractContext(string $content, string $target, int $contextLength = 50): string {
        $pos = mb_strpos($content, $target);
        if ($pos === false) {
            return $target;
        }
        
        $start = max(0, $pos - $contextLength);
        $length = min(mb_strlen($content) - $start, $contextLength * 2 + mb_strlen($target));
        
        return mb_substr($content, $start, $length);
    }

    /**
     * Restore paragraph structure based on original content
     */
    private function restoreParagraphStructure(string $originalContent, string $translatedContent): string {
        // Simple approach: if original has more paragraphs, try to restore some structure
        $originalParagraphs = explode("\n\n", $originalContent);
        $translatedParagraphs = explode("\n\n", $translatedContent);

        if (count($originalParagraphs) > count($translatedParagraphs)) {
            // Try to split long paragraphs in translation
            $sentences = preg_split('/(?<=[.!?])\s+/', $translatedContent);
            $targetParagraphs = count($originalParagraphs);
            $sentencesPerParagraph = ceil(count($sentences) / $targetParagraphs);

            $newParagraphs = [];
            for ($i = 0; $i < count($sentences); $i += $sentencesPerParagraph) {
                $paragraph = implode(' ', array_slice($sentences, $i, $sentencesPerParagraph));
                if (trim($paragraph)) {
                    $newParagraphs[] = trim($paragraph);
                }
            }

            return implode("\n\n", $newParagraphs);
        }

        return $translatedContent;
    }

    /**
     * Fix unmatched quotation marks
     */
    private function fixUnmatchedQuotes(string $content): string {
        // Count quotes
        $doubleQuotes = substr_count($content, '"');

        if ($doubleQuotes % 2 !== 0) {
            // Add missing closing quote at the end if odd number
            $content .= '"';
        }

        return $content;
    }

    /**
     * Fix unmatched parentheses
     */
    private function fixUnmatchedParentheses(string $content): string {
        $openCount = substr_count($content, '(');
        $closeCount = substr_count($content, ')');

        if ($openCount > $closeCount) {
            // Add missing closing parentheses
            $content .= str_repeat(')', $openCount - $closeCount);
        } elseif ($closeCount > $openCount) {
            // Add missing opening parentheses at the beginning
            $content = str_repeat('(', $closeCount - $openCount) . $content;
        }

        return $content;
    }

    /**
     * Fix Japanese punctuation issues in English text
     */
    private function fixJapanesePunctuationIssues(string $content): string {
        // Common Japanese punctuation issues in English translations
        $fixes = [
            // Fix broken Japanese quotes mixed with English
            // Example: "「They「re climbing again!?」" -> "They're climbing again!?"
            '/「([^」]*?)「([^」]*?)」/u' => '"$1\'$2"',
            '/「([^」]*?)』([^」]*?)」/u' => '"$1"$2"',
            '/『([^』]*?)「([^』]*?)』/u' => '"$1\'$2"',

            // Fix standalone Japanese quotes around English text
            '/「([^」]+?)」/u' => '"$1"',
            '/『([^』]+?)』/u' => '"$1"',

            // Fix mixed Japanese and English quotes
            '/「([^」]*?)"([^"]*?)」/u' => '"$1$2"',
            '/"([^"]*?)」([^"]*?)"/u' => '"$1$2"',

            // Fix Japanese punctuation at the end of English sentences
            '/([a-zA-Z0-9])\s*。/u' => '$1.',
            '/([a-zA-Z0-9])\s*！/u' => '$1!',
            '/([a-zA-Z0-9])\s*？/u' => '$1?',
            '/([a-zA-Z0-9])\s*、/u' => '$1,',

            // Fix Japanese ellipsis
            '/…{2,}/u' => '...',
            '/…/u' => '...',

            // Fix Japanese dash/hyphen
            '/ー+/u' => '-',
            '/－+/u' => '-',

            // Fix Japanese parentheses
            '/（([^）]+?)）/u' => '($1)',

            // Fix Japanese brackets
            '/［([^］]+?)］/u' => '[$1]',

            // Fix multiple punctuation issues
            '/[！？]{2,}/u' => '!?',
            '/[？！]{2,}/u' => '?!',

            // Fix space issues around punctuation
            '/\s+([.!?,:;])/u' => '$1',
            '/([.!?])\s*([.!?])/u' => '$1$2',

            // Fix common contraction issues caused by Japanese quotes
            '/([a-zA-Z])「([a-zA-Z])/u' => '$1\'$2',
            '/([a-zA-Z])」([a-zA-Z])/u' => '$1\'$2',
        ];

        foreach ($fixes as $pattern => $replacement) {
            $content = preg_replace($pattern, $replacement, $content);
        }

        // Additional cleanup for specific cases
        $content = $this->fixSpecificPunctuationCases($content);

        return $content;
    }

    /**
     * Fix specific punctuation cases that need special handling
     */
    private function fixSpecificPunctuationCases(string $content): string {
        // Fix cases like "They「re" -> "They're"
        $content = preg_replace('/([a-zA-Z])「([a-zA-Z])/u', '$1\'$2', $content);

        // Fix cases like "don」t" -> "don't"
        $content = preg_replace('/([a-zA-Z])」([a-zA-Z])/u', '$1\'$2', $content);

        // Fix cases like "it」s" -> "it's"
        $content = preg_replace('/([a-zA-Z])」s\b/u', '$1\'s', $content);

        // Fix cases like "I」m" -> "I'm"
        $content = preg_replace('/([a-zA-Z])」m\b/u', '$1\'m', $content);

        // Fix cases like "we」re" -> "we're"
        $content = preg_replace('/([a-zA-Z])」re\b/u', '$1\'re', $content);

        // Fix cases like "you」ll" -> "you'll"
        $content = preg_replace('/([a-zA-Z])」ll\b/u', '$1\'ll', $content);

        // Fix cases like "can」t" -> "can't"
        $content = preg_replace('/([a-zA-Z])」t\b/u', '$1\'t', $content);

        // Fix cases like "won」t" -> "won't"
        $content = preg_replace('/([a-zA-Z])」t\b/u', '$1\'t', $content);

        // Fix cases like "I」ve" -> "I've"
        $content = preg_replace('/([a-zA-Z])」ve\b/u', '$1\'ve', $content);

        // Fix cases like "I」d" -> "I'd"
        $content = preg_replace('/([a-zA-Z])」d\b/u', '$1\'d', $content);

        // Fix double quotes that should be single quotes in contractions
        $content = preg_replace('/([a-zA-Z])"([a-zA-Z])/u', '$1\'$2', $content);

        // Clean up any remaining Japanese punctuation in English context
        $content = preg_replace('/([a-zA-Z0-9\s])[「」『』]/u', '$1', $content);

        return $content;
    }
}
?>
