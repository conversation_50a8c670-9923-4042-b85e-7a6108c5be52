<?php
/**
 * Translation Quality Fix Service
 * Applies automatic corrections to translated content based on quality check results
 */

class TranslationQualityFixService {
    private $db;
    private $nameSubstitutionService;
    private $automaticFormattingService;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->nameSubstitutionService = new NameSubstitutionService();
        $this->automaticFormattingService = new AutomaticFormattingService();
    }
    
    /**
     * Apply corrections to chapter translation
     */
    public function applyCorrections(int $novelId, int $chapterNumber, array $fixTypes, array $corrections): array {
        $startTime = microtime(true);
        
        try {
            // Get current chapter data
            $chapter = $this->getChapterData($novelId, $chapterNumber);
            if (!$chapter) {
                return [
                    'success' => false,
                    'error' => 'Chapter not found or has no translation'
                ];
            }
            
            $originalContent = $chapter['translated_content'];
            $updatedContent = $originalContent;
            $appliedFixes = [];
            
            // Apply corrections based on fix types
            foreach ($fixTypes as $fixType) {
                switch ($fixType) {
                    case 'name_dictionary':
                        $result = $this->applyNameCorrections($updatedContent, $corrections['name_dictionary'] ?? []);
                        $updatedContent = $result['content'];
                        $appliedFixes = array_merge($appliedFixes, $result['fixes']);
                        break;
                        
                    case 'formatting':
                        $result = $this->applyFormattingCorrections($chapter['original_content'], $updatedContent, $corrections['formatting'] ?? []);
                        $updatedContent = $result['content'];
                        $appliedFixes = array_merge($appliedFixes, $result['fixes']);
                        break;
                        
                    case 'punctuation':
                        $result = $this->applyPunctuationCorrections($updatedContent, $corrections['punctuation'] ?? []);
                        $updatedContent = $result['content'];
                        $appliedFixes = array_merge($appliedFixes, $result['fixes']);
                        break;
                }
            }
            
            // Update chapter in database if changes were made
            if ($updatedContent !== $originalContent) {
                $this->updateChapterTranslation($chapter['id'], $updatedContent);
                
                // Log the correction
                $this->logQualityCorrection($novelId, $chapterNumber, $appliedFixes);
            }
            
            return [
                'success' => true,
                'fixes_applied' => count($appliedFixes),
                'updated_content' => $updatedContent,
                'summary' => $this->generateFixSummary($appliedFixes),
                'execution_time' => round(microtime(true) - $startTime, 2)
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to apply corrections: ' . $e->getMessage(),
                'execution_time' => round(microtime(true) - $startTime, 2)
            ];
        }
    }
    
    /**
     * Preview corrections without applying them
     */
    public function previewCorrections(int $novelId, int $chapterNumber, array $corrections): array {
        $chapter = $this->getChapterData($novelId, $chapterNumber);
        if (!$chapter) {
            throw new Exception('Chapter not found');
        }
        
        $preview = [];
        $content = $chapter['translated_content'];
        
        // Preview name corrections
        if (isset($corrections['name_dictionary'])) {
            foreach ($corrections['name_dictionary'] as $correction) {
                $preview[] = [
                    'type' => 'name_dictionary',
                    'description' => $correction['description'],
                    'before' => $this->extractContext($content, $correction['from']),
                    'after' => str_replace($correction['from'], $correction['to'], $this->extractContext($content, $correction['from'])),
                    'confidence' => $correction['confidence']
                ];
            }
        }
        
        // Preview formatting corrections
        if (isset($corrections['formatting'])) {
            foreach ($corrections['formatting'] as $correction) {
                $preview[] = [
                    'type' => 'formatting',
                    'description' => $correction['description'],
                    'action' => $correction['action'],
                    'confidence' => $correction['confidence']
                ];
            }
        }
        
        // Preview punctuation corrections
        if (isset($corrections['punctuation'])) {
            foreach ($corrections['punctuation'] as $correction) {
                $preview[] = [
                    'type' => 'punctuation',
                    'description' => $correction['description'],
                    'action' => $correction['action'],
                    'confidence' => $correction['confidence']
                ];
            }
        }
        
        return $preview;
    }
    
    /**
     * Get chapter data
     */
    private function getChapterData(int $novelId, int $chapterNumber): ?array {
        return $this->db->fetchOne(
            "SELECT id, original_content, translated_content, translation_status 
             FROM chapters 
             WHERE novel_id = ? AND chapter_number = ?",
            [$novelId, $chapterNumber]
        );
    }
    
    /**
     * Apply name dictionary corrections
     */
    private function applyNameCorrections(string $content, array $corrections): array {
        $appliedFixes = [];
        
        foreach ($corrections as $correction) {
            if ($correction['type'] === 'name_replacement' || $correction['type'] === 'name_translation') {
                $before = $content;
                $content = str_replace($correction['from'], $correction['to'], $content);
                
                if ($content !== $before) {
                    $appliedFixes[] = [
                        'type' => 'name_correction',
                        'description' => $correction['description'],
                        'from' => $correction['from'],
                        'to' => $correction['to']
                    ];
                }
            }
        }
        
        return [
            'content' => $content,
            'fixes' => $appliedFixes
        ];
    }
    
    /**
     * Apply formatting corrections
     */
    private function applyFormattingCorrections(string $originalContent, string $translatedContent, array $corrections): array {
        $appliedFixes = [];
        $content = $translatedContent;
        
        foreach ($corrections as $correction) {
            switch ($correction['action']) {
                case 'restore_line_breaks':
                    $result = $this->automaticFormattingService->validateAndEnforceFormatting($originalContent, $content);
                    if ($result['was_restored']) {
                        $content = $result['text'];
                        $appliedFixes[] = [
                            'type' => 'formatting',
                            'description' => 'Restored line breaks to match original structure'
                        ];
                    }
                    break;
                    
                case 'restore_paragraphs':
                    $content = $this->restoreParagraphStructure($originalContent, $content);
                    $appliedFixes[] = [
                        'type' => 'formatting',
                        'description' => 'Restored paragraph structure'
                    ];
                    break;
            }
        }
        
        return [
            'content' => $content,
            'fixes' => $appliedFixes
        ];
    }
    
    /**
     * Apply punctuation corrections
     */
    private function applyPunctuationCorrections(string $content, array $corrections): array {
        $appliedFixes = [];

        // Always apply comprehensive punctuation fixes
        $originalContent = $content;

        // Fix Japanese punctuation issues first
        $content = $this->fixJapanesePunctuationIssues($content);
        if ($content !== $originalContent) {
            $appliedFixes[] = [
                'type' => 'punctuation',
                'description' => 'Fixed Japanese punctuation marks in English text'
            ];
        }

        // Apply specific corrections from quality check
        foreach ($corrections as $correction) {
            switch ($correction['action']) {
                case 'fix_japanese_punctuation':
                    $before = $content;
                    $content = $this->fixJapanesePunctuationIssues($content);
                    if ($content !== $before) {
                        $appliedFixes[] = [
                            'type' => 'punctuation',
                            'description' => 'Fixed Japanese punctuation marks in English text'
                        ];
                    }
                    break;

                case 'fix_quotes':
                    $before = $content;
                    $content = $this->fixUnmatchedQuotes($content);
                    if ($content !== $before) {
                        $appliedFixes[] = [
                            'type' => 'punctuation',
                            'description' => 'Fixed unmatched quotation marks'
                        ];
                    }
                    break;

                case 'fix_parentheses':
                    $before = $content;
                    $content = $this->fixUnmatchedParentheses($content);
                    if ($content !== $before) {
                        $appliedFixes[] = [
                            'type' => 'punctuation',
                            'description' => 'Fixed unmatched parentheses'
                        ];
                    }
                    break;
            }
        }

        return [
            'content' => $content,
            'fixes' => $appliedFixes
        ];
    }
    
    /**
     * Update chapter translation in database
     */
    private function updateChapterTranslation(int $chapterId, string $content): void {
        $this->db->update(
            'chapters',
            ['translated_content' => $content, 'updated_at' => date('Y-m-d H:i:s')],
            'id = ?',
            [$chapterId]
        );
    }
    
    /**
     * Log quality correction for audit trail (optional - won't fail if table doesn't exist)
     */
    private function logQualityCorrection(int $novelId, int $chapterNumber, array $fixes): void {
        try {
            $this->db->insert('quality_corrections_log', [
                'novel_id' => $novelId,
                'chapter_number' => $chapterNumber,
                'fixes_applied' => json_encode($fixes),
                'applied_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            // Log the error but don't fail the operation
            error_log("Quality correction logging failed: " . $e->getMessage());
        }
    }
    
    /**
     * Generate summary of applied fixes
     */
    private function generateFixSummary(array $fixes): array {
        $summary = [
            'total_fixes' => count($fixes),
            'by_type' => []
        ];
        
        foreach ($fixes as $fix) {
            $type = $fix['type'];
            if (!isset($summary['by_type'][$type])) {
                $summary['by_type'][$type] = 0;
            }
            $summary['by_type'][$type]++;
        }
        
        return $summary;
    }
    
    /**
     * Extract context around a specific text for preview
     */
    private function extractContext(string $content, string $target, int $contextLength = 50): string {
        $pos = mb_strpos($content, $target);
        if ($pos === false) {
            return $target;
        }
        
        $start = max(0, $pos - $contextLength);
        $length = min(mb_strlen($content) - $start, $contextLength * 2 + mb_strlen($target));
        
        return mb_substr($content, $start, $length);
    }

    /**
     * Restore paragraph structure based on original content
     */
    private function restoreParagraphStructure(string $originalContent, string $translatedContent): string {
        // Simple approach: if original has more paragraphs, try to restore some structure
        $originalParagraphs = explode("\n\n", $originalContent);
        $translatedParagraphs = explode("\n\n", $translatedContent);

        if (count($originalParagraphs) > count($translatedParagraphs)) {
            // Try to split long paragraphs in translation
            $sentences = preg_split('/(?<=[.!?])\s+/', $translatedContent);
            $targetParagraphs = count($originalParagraphs);
            $sentencesPerParagraph = ceil(count($sentences) / $targetParagraphs);

            $newParagraphs = [];
            for ($i = 0; $i < count($sentences); $i += $sentencesPerParagraph) {
                $paragraph = implode(' ', array_slice($sentences, $i, $sentencesPerParagraph));
                if (trim($paragraph)) {
                    $newParagraphs[] = trim($paragraph);
                }
            }

            return implode("\n\n", $newParagraphs);
        }

        return $translatedContent;
    }

    /**
     * Fix unmatched quotation marks
     */
    private function fixUnmatchedQuotes(string $content): string {
        // Count quotes
        $doubleQuotes = substr_count($content, '"');

        if ($doubleQuotes % 2 !== 0) {
            // Add missing closing quote at the end if odd number
            $content .= '"';
        }

        return $content;
    }

    /**
     * Fix unmatched parentheses
     */
    private function fixUnmatchedParentheses(string $content): string {
        $openCount = substr_count($content, '(');
        $closeCount = substr_count($content, ')');

        if ($openCount > $closeCount) {
            // Add missing closing parentheses
            $content .= str_repeat(')', $openCount - $closeCount);
        } elseif ($closeCount > $openCount) {
            // Add missing opening parentheses at the beginning
            $content = str_repeat('(', $closeCount - $openCount) . $content;
        }

        return $content;
    }

    /**
     * Fix Japanese punctuation issues in English text (preserving outer quote structure)
     */
    private function fixJapanesePunctuationIssues(string $content): string {
        // Simplified, reliable approach focusing on core requirements

        // Step 1: Fix non-structural punctuation (safe operations)
        $content = $this->fixNonStructuralPunctuation($content);

        // Step 2: Fix contractions within quotes while preserving quote structure
        $content = $this->fixContractionsPreservingStructure($content);

        // Step 3: Convert only obvious non-dialogue quotes
        $content = $this->convertObviousNonDialogueQuotes($content);

        return $content;
    }

    /**
     * Fix contractions while preserving quote structure (simplified approach)
     */
    private function fixContractionsPreservingStructure(string $content): string {
        // Fix broken contractions but be very careful about quote boundaries
        $contractionFixes = [
            // Fix contractions with Japanese quote marks
            '/([a-zA-Z])「([a-zA-Z])/u' => '$1\'$2',
            '/([a-zA-Z])」([a-zA-Z])/u' => '$1\'$2',

            // Fix specific contraction endings
            '/([a-zA-Z])」s\b/u' => '$1\'s',
            '/([a-zA-Z])」m\b/u' => '$1\'m',
            '/([a-zA-Z])」re\b/u' => '$1\'re',
            '/([a-zA-Z])」ll\b/u' => '$1\'ll',
            '/([a-zA-Z])」t\b/u' => '$1\'t',
            '/([a-zA-Z])」ve\b/u' => '$1\'ve',
            '/([a-zA-Z])」d\b/u' => '$1\'d',
        ];

        foreach ($contractionFixes as $pattern => $replacement) {
            $content = preg_replace($pattern, $replacement, $content);
        }

        return $content;
    }

    /**
     * Convert only obvious non-dialogue quotes (very conservative)
     */
    private function convertObviousNonDialogueQuotes(string $content): string {
        // Only convert quotes that are clearly not dialogue

        // Convert UI elements (all caps, short)
        $content = preg_replace('/「([A-Z]{1,6})」/u', '"$1"', $content);
        $content = preg_replace('/『([A-Z]{1,6})』/u', '"$1"', $content);

        // Convert quotes preceded by specific words that indicate non-dialogue
        $content = preg_replace('/(the word|click|press|select|choose|button|menu|option)\s+「([^」]{1,15})」/iu', '$1 "$2"', $content);
        $content = preg_replace('/(the word|click|press|select|choose|button|menu|option)\s+『([^』]{1,15})』/iu', '$1 "$2"', $content);

        return $content;
    }

    /**
     * Process dialogue quotes - preserve structure while fixing inner content
     */
    private function processDialogueQuotes(string $content): string {
        // Handle dialogue patterns with context indicators
        $dialogueIndicators = '(he|she|I|they|said|asked|replied|exclaimed|whispered|shouted|thought|muttered|declared|announced)';

        // Process quotes that are clearly dialogue (followed by dialogue indicators)
        $content = preg_replace_callback('/「([^」]+)」(\s*' . $dialogueIndicators . ')/u', function($matches) {
            $innerContent = $this->fixContractionsInText($matches[1]);
            return '「' . $innerContent . '」' . $matches[2];
        }, $content);

        $content = preg_replace_callback('/『([^』]+)』(\s*' . $dialogueIndicators . ')/u', function($matches) {
            $innerContent = $this->fixContractionsInText($matches[1]);
            return '『' . $innerContent . '』' . $matches[2];
        }, $content);

        // Process quotes at the beginning of sentences (likely dialogue)
        $content = preg_replace_callback('/^「([^」]+)」/mu', function($matches) {
            $innerContent = $this->fixContractionsInText($matches[1]);
            return '「' . $innerContent . '」';
        }, $content);

        $content = preg_replace_callback('/^『([^』]+)』/mu', function($matches) {
            $innerContent = $this->fixContractionsInText($matches[1]);
            return '『' . $innerContent . '』';
        }, $content);

        // Process quotes after dialogue indicators
        $content = preg_replace_callback('/(' . $dialogueIndicators . '\s+)「([^」]+)」/u', function($matches) {
            $innerContent = $this->fixContractionsInText($matches[2]);
            return $matches[1] . '「' . $innerContent . '」';
        }, $content);

        $content = preg_replace_callback('/(' . $dialogueIndicators . '\s+)『([^』]+)』/u', function($matches) {
            $innerContent = $this->fixContractionsInText($matches[2]);
            return $matches[1] . '『' . $innerContent . '』';
        }, $content);

        return $content;
    }

    /**
     * Fix remaining contractions that weren't caught by dialogue processing
     */
    private function fixRemainingContractions(string $content): string {
        // Fix any remaining broken contractions outside of preserved dialogue
        $contractionFixes = [
            '/([a-zA-Z])「([a-zA-Z])/u' => '$1\'$2',
            '/([a-zA-Z])」([a-zA-Z])/u' => '$1\'$2',
            '/([a-zA-Z])」s\b/u' => '$1\'s',
            '/([a-zA-Z])」m\b/u' => '$1\'m',
            '/([a-zA-Z])」re\b/u' => '$1\'re',
            '/([a-zA-Z])」ll\b/u' => '$1\'ll',
            '/([a-zA-Z])」t\b/u' => '$1\'t',
            '/([a-zA-Z])」ve\b/u' => '$1\'ve',
            '/([a-zA-Z])」d\b/u' => '$1\'d',
        ];

        foreach ($contractionFixes as $pattern => $replacement) {
            $content = preg_replace($pattern, $replacement, $content);
        }

        return $content;
    }

    /**
     * Fix contractions within Japanese quotes while preserving the quote structure
     */
    private function fixContractionsWithinQuotes(string $content): string {
        // Fix broken contractions within 「」 quotes - handle nested quotes properly
        $content = preg_replace_callback('/「([^」]*(?:「[^」]*」[^」]*)*)」/u', function($matches) {
            $innerContent = $matches[1];
            // Fix contractions within the quote, but handle nested quotes
            $innerContent = $this->fixContractionsInText($innerContent);
            return '「' . $innerContent . '」';
        }, $content);

        // Fix broken contractions within 『』 quotes - handle nested quotes properly
        $content = preg_replace_callback('/『([^』]*(?:『[^』]*』[^』]*)*)』/u', function($matches) {
            $innerContent = $matches[1];
            // Fix contractions within the quote, but handle nested quotes
            $innerContent = $this->fixContractionsInText($innerContent);
            return '『' . $innerContent . '』';
        }, $content);

        return $content;
    }

    /**
     * Fix contractions in text (helper method)
     */
    private function fixContractionsInText(string $text): string {
        // Fix common contractions with Japanese quote marks
        $contractionFixes = [
            // Fix broken contractions
            '/([a-zA-Z])「([a-zA-Z])/u' => '$1\'$2',
            '/([a-zA-Z])」([a-zA-Z])/u' => '$1\'$2',

            // Fix specific contraction patterns
            '/([a-zA-Z])」s\b/u' => '$1\'s',
            '/([a-zA-Z])」m\b/u' => '$1\'m',
            '/([a-zA-Z])」re\b/u' => '$1\'re',
            '/([a-zA-Z])」ll\b/u' => '$1\'ll',
            '/([a-zA-Z])」t\b/u' => '$1\'t',
            '/([a-zA-Z])」ve\b/u' => '$1\'ve',
            '/([a-zA-Z])」d\b/u' => '$1\'d',

            // Fix cases where contraction is at the beginning
            '/\b「([a-zA-Z])/u' => '\'$1',
        ];

        foreach ($contractionFixes as $pattern => $replacement) {
            $text = preg_replace($pattern, $replacement, $text);
        }

        // Handle nested quotes within the text - convert inner quotes to English
        $text = $this->convertInnerQuotes($text);

        return $text;
    }

    /**
     * Convert inner quotes within dialogue to English quotes
     */
    private function convertInnerQuotes(string $text): string {
        // Only convert inner quotes that are clearly not part of the main dialogue structure
        // Be very conservative - only convert obvious cases like single words or short phrases

        // Convert single words or very short phrases that are clearly quoted items, not dialogue
        $text = preg_replace('/「([A-Za-z]{1,8})」/u', '"$1"', $text);
        $text = preg_replace('/『([A-Za-z]{1,8})』/u', '"$1"', $text);

        // Convert obvious non-dialogue patterns (numbers, single letters, etc.)
        $text = preg_replace('/「([0-9]+)」/u', '"$1"', $text);
        $text = preg_replace('/『([0-9]+)』/u', '"$1"', $text);

        return $text;
    }

    /**
     * Fix non-structural punctuation issues
     */
    private function fixNonStructuralPunctuation(string $content): string {
        $fixes = [
            // Fix Japanese punctuation at the end of English sentences
            '/([a-zA-Z0-9])\s*。/u' => '$1.',
            '/([a-zA-Z0-9])\s*！/u' => '$1!',
            '/([a-zA-Z0-9])\s*？/u' => '$1?',
            '/([a-zA-Z0-9])\s*、/u' => '$1,',

            // Fix Japanese ellipsis
            '/…{2,}/u' => '...',
            '/…/u' => '...',

            // Fix Japanese dash/hyphen
            '/ー+/u' => '-',
            '/－+/u' => '-',

            // Fix Japanese parentheses
            '/（([^）]+?)）/u' => '($1)',

            // Fix Japanese brackets
            '/［([^］]+?)］/u' => '[$1]',

            // Fix multiple punctuation issues
            '/[！？]{2,}/u' => '!?',
            '/[？！]{2,}/u' => '?!',

            // Fix space issues around punctuation
            '/\s+([.!?,:;])/u' => '$1',
            '/([.!?])\s*([.!?])/u' => '$1$2',
        ];

        foreach ($fixes as $pattern => $replacement) {
            $content = preg_replace($pattern, $replacement, $content);
        }

        return $content;
    }

    /**
     * Convert standalone Japanese quotes that are clearly not dialogue structure
     */
    private function convertStandaloneQuotes(string $content): string {
        // Only convert Japanese quotes that are clearly not dialogue structure
        // Be very conservative to preserve intentional dialogue formatting

        $dialogueIndicators = '(he|she|I|they|said|asked|replied|exclaimed|whispered|shouted|thought|muttered|declared|announced)';

        // Convert quotes that are clearly UI elements or labels (all caps, short)
        $content = preg_replace('/「([A-Z]{1,6})」/u', '"$1"', $content);
        $content = preg_replace('/『([A-Z]{1,6})』/u', '"$1"', $content);

        // Convert very short quoted text in the middle of sentences (not at start/end, not near dialogue indicators)
        $content = preg_replace('/(?<!\s)(\w+)\s+「([^」]{1,8})」\s+(\w+)(?!\s*' . $dialogueIndicators . ')/u', '$1 "$2" $3', $content);
        $content = preg_replace('/(?<!\s)(\w+)\s+『([^』]{1,8})』\s+(\w+)(?!\s*' . $dialogueIndicators . ')/u', '$1 "$2" $3', $content);

        // Convert quotes that are clearly labels or references (preceded by "the word", "click", etc.)
        $content = preg_replace('/(the word|click|press|select|choose)\s+「([^」]{1,15})」/iu', '$1 "$2"', $content);
        $content = preg_replace('/(the word|click|press|select|choose)\s+『([^』]{1,15})』/iu', '$1 "$2"', $content);

        return $content;
    }

    /**
     * Fix specific punctuation cases that need special handling (legacy method - now mostly handled by new methods)
     */
    private function fixSpecificPunctuationCases(string $content): string {
        // This method is now mostly redundant as the new approach handles these cases better
        // Keeping it for any edge cases that might slip through

        // Fix any remaining broken contractions outside of quote structures
        $content = preg_replace('/([a-zA-Z])「([a-zA-Z])/u', '$1\'$2', $content);
        $content = preg_replace('/([a-zA-Z])」([a-zA-Z])/u', '$1\'$2', $content);

        // Fix double quotes that should be single quotes in contractions (outside of dialogue)
        $content = preg_replace('/([a-zA-Z])"([a-zA-Z])/u', '$1\'$2', $content);

        return $content;
    }
}
?>
