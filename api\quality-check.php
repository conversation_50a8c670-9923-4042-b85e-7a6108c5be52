<?php
/**
 * API Endpoint: Translation Quality Check
 * POST /api/quality-check.php - Perform quality check on translated content
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../classes/TranslationQualityCheckService.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

$method = $_SERVER['REQUEST_METHOD'];

// Handle preflight requests
if ($method === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    switch ($method) {
        case 'POST':
            // Check if this is a bulk operation or single chapter
            $rawInput = file_get_contents('php://input');
            $input = json_decode($rawInput, true);

            if (isset($input['action'])) {
                switch ($input['action']) {
                    case 'bulk_check':
                        handleBulkQualityCheck();
                        break;
                    case 'get_providers':
                        getAvailableProviders();
                        break;
                    default:
                        handleQualityCheck();
                }
            } else {
                handleQualityCheck();
            }
            break;
        case 'GET':
            // Handle GET request for provider list
            if (isset($_GET['action']) && $_GET['action'] === 'providers') {
                getAvailableProviders();
            } else {
                jsonResponse(['error' => 'Invalid GET request'], 400);
            }
            break;
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    logError('Quality Check API Error: ' . $e->getMessage(), [
        'method' => $method,
        'trace' => $e->getTraceAsString()
    ]);

    jsonResponse([
        'success' => false,
        'error' => 'Quality check failed: ' . $e->getMessage()
    ], 500);
}

/**
 * Handle quality check request
 */
function handleQualityCheck() {
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required parameters
    $novelId = (int)($input['novel_id'] ?? 0);
    $chapterNumber = (int)($input['chapter_number'] ?? 0);
    $aiProvider = $input['ai_provider'] ?? null;

    if (!$novelId || !$chapterNumber) {
        jsonResponse(['error' => 'Novel ID and chapter number are required'], 400);
    }

    // Validate AI provider if specified
    if ($aiProvider && !array_key_exists($aiProvider, SUPPORTED_AI_PROVIDERS)) {
        jsonResponse(['error' => 'Invalid AI provider specified'], 400);
    }

    logError('Quality Check API: Request received', [
        'novel_id' => $novelId,
        'chapter_number' => $chapterNumber,
        'ai_provider' => $aiProvider,
        'raw_input' => $rawInput
    ]);

    try {
        // Initialize quality check service
        $qualityCheckService = new TranslationQualityCheckService();

        // Perform quality check
        $result = $qualityCheckService->performQualityCheck($novelId, $chapterNumber, $aiProvider);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'quality_report' => $result['quality_report']
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => $result['error']
            ], 400);
        }

    } catch (Exception $e) {
        logError('Quality check execution error: ' . $e->getMessage(), [
            'novel_id' => $novelId,
            'chapter_number' => $chapterNumber,
            'ai_provider' => $aiProvider,
            'trace' => $e->getTraceAsString()
        ]);

        jsonResponse([
            'success' => false,
            'error' => 'Quality check failed: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Handle bulk quality check request (for multiple chapters)
 */
function handleBulkQualityCheck() {
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required parameters
    $novelId = (int)($input['novel_id'] ?? 0);
    $chapterNumbers = $input['chapter_numbers'] ?? [];
    $aiProvider = $input['ai_provider'] ?? null;

    if (!$novelId || empty($chapterNumbers) || !is_array($chapterNumbers)) {
        jsonResponse(['error' => 'Novel ID and chapter numbers array are required'], 400);
    }

    // Validate AI provider if specified
    if ($aiProvider && !array_key_exists($aiProvider, SUPPORTED_AI_PROVIDERS)) {
        jsonResponse(['error' => 'Invalid AI provider specified'], 400);
    }

    // Limit bulk operations to prevent timeouts
    if (count($chapterNumbers) > 10) {
        jsonResponse(['error' => 'Maximum 10 chapters allowed for bulk quality check'], 400);
    }

    logError('Bulk Quality Check API: Request received', [
        'novel_id' => $novelId,
        'chapter_count' => count($chapterNumbers),
        'ai_provider' => $aiProvider
    ]);

    try {
        // Initialize quality check service
        $qualityCheckService = new TranslationQualityCheckService();

        $results = [];
        $successCount = 0;
        $errorCount = 0;

        foreach ($chapterNumbers as $chapterNumber) {
            try {
                $result = $qualityCheckService->performQualityCheck($novelId, (int)$chapterNumber, $aiProvider);
                
                if ($result['success']) {
                    $successCount++;
                    $results[] = [
                        'chapter_number' => $chapterNumber,
                        'success' => true,
                        'quality_report' => $result['quality_report']
                    ];
                } else {
                    $errorCount++;
                    $results[] = [
                        'chapter_number' => $chapterNumber,
                        'success' => false,
                        'error' => $result['error']
                    ];
                }
            } catch (Exception $e) {
                $errorCount++;
                $results[] = [
                    'chapter_number' => $chapterNumber,
                    'success' => false,
                    'error' => 'Quality check failed: ' . $e->getMessage()
                ];
            }
        }

        jsonResponse([
            'success' => true,
            'summary' => [
                'total_chapters' => count($chapterNumbers),
                'successful_checks' => $successCount,
                'failed_checks' => $errorCount
            ],
            'results' => $results
        ]);

    } catch (Exception $e) {
        logError('Bulk quality check execution error: ' . $e->getMessage(), [
            'novel_id' => $novelId,
            'chapter_count' => count($chapterNumbers),
            'ai_provider' => $aiProvider,
            'trace' => $e->getTraceAsString()
        ]);

        jsonResponse([
            'success' => false,
            'error' => 'Bulk quality check failed: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get available AI providers for quality check
 */
function getAvailableProviders() {
    $providers = [];
    
    foreach (SUPPORTED_AI_PROVIDERS as $key => $config) {
        $providers[] = [
            'key' => $key,
            'name' => $config['name'],
            'description' => $config['description']
        ];
    }

    jsonResponse([
        'success' => true,
        'providers' => $providers
    ]);
}
?>
