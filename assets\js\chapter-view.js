/**
 * Chapter View JavaScript
 * Handles chapter viewing and editing functionality
 */

class ChapterView {
    constructor(novelId, chapterNumber) {
        this.novelId = novelId;
        this.chapterNumber = chapterNumber;
        this.chapter = null;
        this.novel = null;

        // Handle browser back/forward navigation
        window.addEventListener('popstate', () => {
            this.handleUrlChange();
        });

        this.loadChapter();
        this.initializeBackToTop();
    }

    handleUrlChange() {
        const urlParams = new URLSearchParams(window.location.search);
        const newChapterNumber = parseInt(urlParams.get('chapter'));

        if (newChapterNumber && newChapterNumber !== this.chapterNumber) {
            this.chapterNumber = newChapterNumber;
            this.loadChapter();
        }
    }

    async loadChapter() {
        utils.showLoading(true);

        try {
            // Load specific chapter directly - much more efficient for large novels
            const result = await utils.makeApiRequest(`api/chapter.php?novel_id=${this.novelId}&chapter=${this.chapterNumber}`);

            if (result.success) {
                this.novel = result.data.novel;
                this.chapter = result.data.chapter;
                this.nameDictionary = result.data.name_dictionary;

                // Debug logging
                console.log('Chapter loaded successfully:', {
                    novelId: this.novelId,
                    chapterNumber: this.chapterNumber,
                    chapterTitle: this.chapter.original_title,
                    hasContent: this.chapter.original_content ? 'Yes' : 'No',
                    translationStatus: this.chapter.translation_status
                });

                // Load WordPress posting status if chapter has translated content
                if (this.chapter.translated_content) {
                    await this.loadWordPressStatus();
                }

                this.displayChapter();
            } else {
                this.displayError(result.error || 'Failed to load chapter');
            }
        } catch (error) {
            console.error('Load chapter error:', error);
            this.displayError('Network error occurred: ' + error.message);
        } finally {
            utils.showLoading(false);
        }
    }

    async loadWordPressStatus() {
        try {
            const result = await utils.makeApiRequest(`api/wordpress.php?chapter_id=${this.chapter.id}`);
            if (result.success) {
                this.wordpressStatus = result.status;
            }
        } catch (error) {
            console.warn('Failed to load WordPress status:', error);
            this.wordpressStatus = { posted: false };
        }
    }

    displayChapter() {
        const chapterHtml = `
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <!-- Chapter Header -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <!-- Chapter Title Section -->
                            <div class="chapter-header-title">
                                <h4 class="mb-1">
                                    Chapter ${this.chapter.chapter_number}:
                                    ${utils.escapeHtml(this.chapter.translated_title || this.chapter.original_title)}
                                </h4>
                                <small class="text-muted">
                                    From: ${utils.escapeHtml(this.novel.translated_title || this.novel.original_title)}
                                </small>
                            </div>
                            <!-- Action Buttons Section -->
                            <div class="chapter-header-actions">
                                ${this.renderActionButtons()}
                            </div>
                        </div>
                        <div class="card-body">
                            ${this.renderFuriganaControls()}
                            ${this.renderDebugControls()}
                            <div class="chapter-meta">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="meta-item">
                                            <strong>Status:</strong>
                                            <span class="badge bg-${this.getStatusColor(this.chapter.translation_status)}">
                                                ${utils.getStatusText(this.chapter.translation_status)}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="meta-item">
                                            <strong>Last Updated:</strong>
                                            ${utils.formatDate(this.chapter.updated_at)}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chapter Navigation -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <button class="btn btn-outline-primary" 
                                        onclick="chapterView.navigateChapter(-1)"
                                        ${this.chapterNumber <= 1 ? 'disabled' : ''}>
                                    <i class="fas fa-chevron-left me-1"></i>
                                    Previous Chapter
                                </button>
                                <span class="text-muted">
                                    Chapter ${this.chapterNumber} of ${this.novel.total_chapters}
                                </span>
                                <button class="btn btn-outline-primary" 
                                        onclick="chapterView.navigateChapter(1)"
                                        ${this.chapterNumber >= this.novel.total_chapters ? 'disabled' : ''}>
                                    Next Chapter
                                    <i class="fas fa-chevron-right ms-1"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Chapter Content -->
                    ${this.renderChapterContent()}
                </div>
            </div>
        `;

        document.getElementById('chapter-content').innerHTML = chapterHtml;

        // Initialize furigana display preference
        this.initializeFuriganaDisplay();

        // Re-initialize back to top button after content load
        this.initializeBackToTop();
    }

    initializeFuriganaDisplay() {
        const savedMode = localStorage.getItem('furigana-display-mode') || 'ruby';
        const selectElement = document.getElementById('furigana-display-mode');

        if (selectElement) {
            selectElement.value = savedMode;
            this.changeFuriganaDisplay(savedMode);
        }
    }

    renderActionButtons() {
        let buttons = [];

        // Save button for chapters without content
        if (!this.chapter.original_content) {
            buttons.push(`
                <button class="btn btn-outline-primary btn-sm"
                        onclick="chapterView.saveChapter()"
                        ${this.chapter.translation_status === 'translating' ? 'disabled' : ''}>
                    <i class="fas fa-download me-1"></i>
                    Save Content
                </button>
            `);
        }

        // Translate button for saved chapters
        if (this.chapter.original_content && this.chapter.translation_status !== 'completed') {
            buttons.push(`
                <button class="btn btn-outline-success btn-sm"
                        onclick="chapterView.translateChapter()"
                        ${this.chapter.translation_status === 'translating' ? 'disabled' : ''}>
                    <i class="fas fa-language me-1"></i>
                    ${this.chapter.translation_status === 'translating' ? 'Translating...' : 'Translate'}
                </button>
            `);
        }

        // Rich Editor button for translated chapters
        if (this.chapter.translated_content) {
            buttons.push(`
                <a href="chapter-edit.php?novel_id=${this.novelId}&chapter=${this.chapterNumber}"
                   class="btn btn-outline-primary btn-sm"
                   title="Edit translation with rich text editor">
                    <i class="fas fa-edit me-1"></i>
                    Edit Translation
                </a>
            `);
        }

        // Quality Check button for translated chapters
        if (this.chapter.translated_content) {
            buttons.push(`
                <button class="btn btn-outline-info btn-sm"
                        onclick="chapterView.showQualityCheckModal()"
                        title="Check translation quality and consistency">
                    <i class="fas fa-check-circle me-1"></i>
                    Quality Check
                </button>
            `);
        }

        // Export to Word buttons for translated chapters
        if (this.chapter.translated_content) {
            buttons.push(`
                <div class="btn-group">
                    <button class="btn btn-outline-info btn-sm"
                            onclick="chapterView.exportToWordSimple()"
                            title="Export chapter to Word (simple, compatible version)">
                        <i class="fas fa-file-word me-1"></i>
                        Export Word
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm dropdown-toggle dropdown-toggle-split"
                            data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="visually-hidden">Toggle Dropdown</span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="chapterView.exportToWordSimple()">
                            <i class="fas fa-file-word me-2"></i>Simple Export (Recommended)
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="chapterView.exportToWord()">
                            <i class="fas fa-file-alt me-2"></i>Full Export (Advanced)
                        </a></li>
                    </ul>
                </div>
            `);
        }

        // WordPress posting button for translated chapters
        if (this.chapter.translated_content) {
            const isPosted = this.wordpressStatus && this.wordpressStatus.posted;
            const postData = this.wordpressStatus && this.wordpressStatus.post_data;

            if (isPosted && postData) {
                buttons.push(`
                    <button class="btn btn-success btn-sm"
                            onclick="window.open('${postData.wordpress_url}', '_blank')"
                            title="View on WordPress">
                        <i class="fas fa-check me-1"></i>
                        Posted
                    </button>
                `);
            } else {
                buttons.push(`
                    <div class="btn-group">
                        <button class="btn btn-outline-success btn-sm"
                                onclick="chapterView.showWordPressProfileSelector()"
                                id="wordpress-post-btn"
                                title="Post chapter to WordPress website">
                            <i class="fab fa-wordpress me-1"></i>
                            Post to WordPress
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm dropdown-toggle dropdown-toggle-split"
                                data-bs-toggle="dropdown" aria-expanded="false">
                            <span class="visually-hidden">Toggle Dropdown</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><h6 class="dropdown-header">Select WordPress Profile</h6></li>
                            <li><a class="dropdown-item" href="#" onclick="chapterView.showWordPressProfileSelector()">
                                <i class="fas fa-list me-2"></i>Choose Profile...
                            </a></li>
                        </ul>
                    </div>
                `);
            }
        }

        // Management buttons (for chapters with content)
        if (this.chapter.original_content) {
            // Split Chapter button (only for chapters without chunks)
            if (!this.chapter.has_chunks || this.chapter.chunk_count === 0) {
                buttons.push(`
                    <button class="btn btn-outline-success btn-sm"
                            onclick="chapterView.showSplitChapterModal()"
                            title="Split chapter into smaller parts for easier translation">
                        <i class="fas fa-cut me-1"></i>
                        Split Chapter
                    </button>
                `);
            }

            // Edit Original Text button
            buttons.push(`
                <button class="btn btn-outline-info btn-sm"
                        onclick="chapterView.showEditOriginalModal()"
                        title="Manually edit the original chapter content">
                    <i class="fas fa-edit me-1"></i>
                    Edit Original Text
                </button>
            `);

            // Recrawl button (only for non-manual chapters)
            if (this.novel.platform !== 'manual' && this.chapter.chapter_url) {
                buttons.push(`
                    <button class="btn btn-outline-warning btn-sm"
                            onclick="chapterView.showRecrawlConfirmation()"
                            title="Re-extract content from source">
                        <i class="fas fa-sync-alt me-1"></i>
                        Recrawl Content
                    </button>
                `);
            }

            // Clear content button
            buttons.push(`
                <button class="btn btn-outline-danger btn-sm"
                        onclick="chapterView.showDeleteConfirmation()"
                        title="Clear chapter content while preserving the chapter entry">
                    <i class="fas fa-eraser me-1"></i>
                    Clear Content
                </button>
            `);
        }

        // Navigation buttons
        if (this.chapterNumber > 1) {
            buttons.push(`
                <button class="btn btn-outline-secondary btn-sm"
                        onclick="chapterView.navigateChapter(-1)">
                    <i class="fas fa-chevron-left me-1"></i>
                    Previous
                </button>
            `);
        }

        if (this.chapterNumber < this.novel.total_chapters) {
            buttons.push(`
                <button class="btn btn-outline-secondary btn-sm"
                        onclick="chapterView.navigateChapter(1)">
                    Next
                    <i class="fas fa-chevron-right ms-1"></i>
                </button>
            `);
        }

        return buttons.join(' ');
    }

    renderChapterContent() {
        if (!this.chapter.original_content && !this.chapter.translated_content) {
            return `
                <div class="card">
                    <div class="card-body text-center text-muted">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <h5>No content available</h5>
                        <p>This chapter hasn't been saved yet. Click "Save Content" to download it from the source.</p>
                    </div>
                </div>
            `;
        }

        let contentHtml = '';

        // Add section controls if both original and translated content exist
        if (this.chapter.original_content && this.chapter.translated_content) {
            contentHtml += `
                <div class="section-controls mb-3">
                    <button class="btn btn-outline-primary btn-sm" onclick="chapterView.expandAllSections()">
                        <i class="fas fa-expand-arrows-alt me-1"></i>
                        Show Both
                    </button>
                    <button class="btn btn-outline-secondary btn-sm ms-2" onclick="chapterView.showOriginalOnly()">
                        <i class="fas fa-file-alt me-1"></i>
                        Original Only
                    </button>
                    <button class="btn btn-outline-secondary btn-sm ms-2" onclick="chapterView.showTranslatedOnly()">
                        <i class="fas fa-language me-1"></i>
                        Translation Only
                    </button>
                    <button class="btn btn-outline-secondary btn-sm ms-2" onclick="chapterView.collapseAllSections()">
                        <i class="fas fa-compress-arrows-alt me-1"></i>
                        Hide Both
                    </button>
                </div>
            `;
        }

        // Original content section
        if (this.chapter.original_content) {
            const isExpanded = this.chapter.translated_content ? false : true; // Auto-expand if only original content
            contentHtml += `
                <div class="content-section mb-4">
                    <div class="section-header">
                        <button class="btn btn-link section-toggle ${isExpanded ? '' : 'collapsed'}"
                                type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#original-content-section"
                                aria-expanded="${isExpanded ? 'true' : 'false'}"
                                onclick="chapterView.toggleSection(this)">
                            <i class="fas fa-chevron-${isExpanded ? 'down' : 'right'} toggle-icon"></i>
                            <span class="section-title">
                                <i class="fas fa-file-alt me-2"></i>
                                Original Content (${this.novel.platform === 'shuba69' ? 'Chinese' : 'Japanese'})
                            </span>
                        </button>
                    </div>
                    <div class="collapse ${isExpanded ? 'show' : ''}" id="original-content-section">
                        <div class="card">
                            <div class="card-body">
                                <div class="chapter-text original-text">
                                    ${this.formatSimpleChapterText(this.chapter.original_content)}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Translated content section
        if (this.chapter.translated_content) {
            const isExpanded = true; // Always expand translated content by default
            contentHtml += `
                <div class="content-section">
                    <div class="section-header">
                        <button class="btn btn-link section-toggle ${isExpanded ? '' : 'collapsed'}"
                                type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#translated-content-section"
                                aria-expanded="${isExpanded ? 'true' : 'false'}"
                                onclick="chapterView.toggleSection(this)">
                            <i class="fas fa-chevron-${isExpanded ? 'down' : 'right'} toggle-icon"></i>
                            <span class="section-title">
                                <i class="fas fa-language me-2"></i>
                                English Translation
                            </span>
                        </button>
                    </div>
                    <div class="collapse ${isExpanded ? 'show' : ''}" id="translated-content-section">
                        <div class="card">
                            <div class="card-body">
                                <div class="chapter-text translated-text">
                                    ${this.formatSimpleChapterText(this.chapter.translated_content)}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        return contentHtml;
    }

    formatSimpleChapterText(text) {
        if (!text) return '';

        // Check if debug mode is enabled for chunk markers
        const showChunkMarkers = localStorage.getItem('show-chunk-markers') === 'true';

        // Split into paragraphs while preserving formatting
        const paragraphs = this.splitTextIntoParagraphs(text);

        return paragraphs
            .map(paragraph => {
                // Check if this is a chunk boundary marker
                if (paragraph.startsWith('<!-- CHUNK_BOUNDARY:')) {
                    return showChunkMarkers ? this.formatChunkBoundaryMarker(paragraph) : '';
                }

                // Regular paragraph formatting
                const trimmed = paragraph.trim();
                if (!trimmed) return '';

                // Check if paragraph already contains HTML formatting
                if (this.containsHtmlTags(trimmed)) {
                    // Content has HTML formatting - preserve it but handle furigana
                    const formattedContent = this.formatTextWithFurigana(trimmed);

                    // If it's not already wrapped in a paragraph tag, wrap it
                    if (!trimmed.startsWith('<p>')) {
                        return `<p>${formattedContent}</p>`;
                    }
                    return formattedContent;
                } else {
                    // Plain text content - wrap in paragraph and format
                    return `<p>${this.formatTextWithFurigana(trimmed)}</p>`;
                }
            })
            .filter(p => p) // Remove empty paragraphs
            .join('');
    }

    /**
     * Split text into paragraphs while preserving structure
     */
    splitTextIntoParagraphs(text) {
        // Split on double newlines (paragraph breaks) but preserve single newlines within paragraphs
        return text.split(/\n\s*\n/)
            .map(paragraph => paragraph.trim())
            .filter(paragraph => paragraph);
    }

    /**
     * Format chunk boundary marker for debug display
     */
    formatChunkBoundaryMarker(marker) {
        const match = marker.match(/CHUNK_BOUNDARY:\s*(\d+)\/(\d+)/);
        if (match) {
            const [, current, total] = match;
            return `
                <div class="chunk-boundary-marker">
                    <hr class="chunk-divider">
                    <small class="chunk-marker-text">
                        <i class="fas fa-cut"></i>
                        Chunk ${current} of ${total}
                    </small>
                </div>
            `;
        }
        return '';
    }

    formatTextWithFurigana(text) {
        if (!text) return '';

        // Check if text contains HTML formatting tags
        if (this.containsHtmlTags(text)) {
            // Text has HTML formatting - preserve it and only handle furigana
            const furiganaPattern = /\{([^|]+)\|([^}]+)\}/g;
            return text.replace(furiganaPattern, (_, kanji, furigana) => {
                return `<ruby>${kanji}<rt>${furigana}</rt></ruby>`;
            });
        } else {
            // Plain text - escape HTML and handle furigana
            const furiganaPattern = /\{([^|]+)\|([^}]+)\}/g;
            return utils.escapeHtml(text).replace(furiganaPattern, (_, kanji, furigana) => {
                return `<ruby>${kanji}<rt>${furigana}</rt></ruby>`;
            });
        }
    }

    /**
     * Check if text contains HTML tags (excluding furigana markup)
     */
    containsHtmlTags(text) {
        // Look for HTML tags but exclude furigana markup {kanji|furigana}
        const htmlTagPattern = /<[^>]+>/;
        const furiganaPattern = /\{[^|]+\|[^}]+\}/g;

        // Remove furigana markup first, then check for HTML tags
        const textWithoutFurigana = text.replace(furiganaPattern, '');
        return htmlTagPattern.test(textWithoutFurigana);
    }

    renderFuriganaControls() {
        // Check if chapter has furigana
        const hasFurigana = this.chapter && (
            (this.chapter.original_content && this.chapter.original_content.includes('{')) ||
            (this.chapter.furigana_count && this.chapter.furigana_count > 0)
        );

        if (!hasFurigana) {
            return '';
        }

        return `
            <div class="furigana-controls">
                <label for="furigana-display-mode">Furigana Display:</label>
                <select id="furigana-display-mode" class="form-select form-select-sm d-inline-block w-auto"
                        onchange="chapterView.changeFuriganaDisplay(this.value)">
                    <option value="ruby">Ruby Text (Above)</option>
                    <option value="parentheses">Parentheses</option>
                    <option value="hidden">Hidden</option>
                </select>
                <small class="text-muted ms-3">
                    <i class="fas fa-info-circle"></i>
                    ${this.chapter.furigana_count || 0} furigana found
                </small>
            </div>
        `;
    }

    renderDebugControls() {
        // Check if this chapter has chunks (for debug display)
        const hasChunks = this.chapter && this.chapter.has_chunks;

        if (!hasChunks) {
            return '';
        }

        const showChunkMarkers = localStorage.getItem('show-chunk-markers') === 'true';

        return `
            <div class="debug-controls">
                <label>
                    <input type="checkbox"
                           ${showChunkMarkers ? 'checked' : ''}
                           onchange="chapterView.toggleChunkMarkers(this.checked)">
                    Show chunk boundaries
                </label>
                <small class="text-muted ms-3">
                    <i class="fas fa-info-circle"></i>
                    This chapter was split into chunks for translation
                </small>
            </div>
        `;
    }

    async toggleChunkMarkers(show) {
        localStorage.setItem('show-chunk-markers', show ? 'true' : 'false');

        // Sync preference with backend for future translations
        try {
            await utils.makeApiRequest('api/preferences.php', {
                method: 'POST',
                body: JSON.stringify({
                    preference_key: 'show_chunk_markers',
                    preference_value: show ? 'true' : 'false'
                })
            });
        } catch (error) {
            console.warn('Failed to sync chunk markers preference with backend:', error);
        }

        this.displayChapter(); // Refresh display
    }

    changeFuriganaDisplay(mode) {
        const chapterContent = document.getElementById('chapter-content');
        if (!chapterContent) return;

        // Remove existing furigana classes
        chapterContent.classList.remove('furigana-parentheses', 'furigana-hidden');

        // Apply new mode
        switch (mode) {
            case 'parentheses':
                chapterContent.classList.add('furigana-parentheses');
                break;
            case 'hidden':
                chapterContent.classList.add('furigana-hidden');
                break;
            case 'ruby':
            default:
                // Default ruby display, no additional classes needed
                break;
        }

        // Save preference
        localStorage.setItem('furigana-display-mode', mode);
    }

    formatChapterText(text) {
        // This method is kept for backward compatibility but now just calls formatSimpleChapterText
        return this.formatSimpleChapterText(text);
    }

    getStatusColor(status) {
        const colors = {
            'pending': 'secondary',
            'saved': 'info',
            'translating': 'warning',
            'completed': 'success',
            'error': 'danger'
        };
        return colors[status] || 'secondary';
    }

    async saveChapter() {
        utils.showLoading(true);

        try {
            const result = await utils.makeApiRequest('api/chapters.php', {
                method: 'POST',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    chapter_number: this.chapterNumber
                })
            });

            if (result.success) {
                utils.showToast('Chapter saved successfully', 'success');
                this.loadChapter(); // Refresh
            } else {
                utils.showToast(result.error || 'Failed to save chapter', 'error');
            }
        } catch (error) {
            console.error('Save chapter error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async translateChapter() {
        // Create progress bar container if it doesn't exist
        let progressContainer = document.getElementById('chapter-translation-progress');
        if (!progressContainer) {
            progressContainer = document.createElement('div');
            progressContainer.id = 'chapter-translation-progress';
            progressContainer.className = 'mb-3';

            // Insert before the chapter content
            const chapterContent = document.querySelector('.chapter-content');
            if (chapterContent) {
                chapterContent.parentNode.insertBefore(progressContainer, chapterContent);
            }
        }

        // Create progress bar
        const progressBar = utils.createProgressBar('chapter-translation-progress', {
            title: `Translating Chapter ${this.chapterNumber}`,
            showPercentage: true,
            showStatus: true,
            animated: true,
            color: 'primary'
        });

        if (!progressBar) {
            console.warn('Failed to create progress bar, continuing without progress indication');
        }

        try {
            const result = await utils.makeTranslationRequest('api/chapters.php', {
                novel_id: this.novelId,
                chapter_number: this.chapterNumber,
                target_language: 'en'
            }, progressBar);

            if (result.success) {
                utils.showToast('Chapter translated successfully', 'success');
                this.loadChapter(); // Refresh
            } else {
                utils.showToast(result.error || 'Failed to translate chapter', 'error');
            }
        } catch (error) {
            console.error('Translate chapter error:', error);
            utils.showToast('Network error occurred', 'error');
        }
    }

    async exportToWord() {
        try {
            utils.showLoading(true);

            // Create the export URL
            const exportUrl = `api/export-word.php?novel_id=${this.novelId}&chapter=${this.chapterNumber}`;

            // Create a temporary link to trigger download
            const link = document.createElement('a');
            link.href = exportUrl;
            link.style.display = 'none';
            document.body.appendChild(link);

            // Trigger download
            link.click();

            // Clean up
            document.body.removeChild(link);

            utils.showToast('Word document export started (full version)', 'success');

        } catch (error) {
            console.error('Export to Word error:', error);
            utils.showToast('Failed to export to Word', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async exportToWordSimple() {
        try {
            utils.showLoading(true);

            // Create the simple export URL
            const exportUrl = `api/export-word-simple.php?novel_id=${this.novelId}&chapter=${this.chapterNumber}`;

            // Create a temporary link to trigger download
            const link = document.createElement('a');
            link.href = exportUrl;
            link.style.display = 'none';
            document.body.appendChild(link);

            // Trigger download
            link.click();

            // Clean up
            document.body.removeChild(link);

            utils.showToast('Word document export started (simple version)', 'success');

        } catch (error) {
            console.error('Export to Word Simple error:', error);
            utils.showToast('Failed to export to Word', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async showWordPressProfileSelector() {
        if (!this.chapter.translated_content) {
            utils.showToast('Chapter must be translated before posting to WordPress', 'error');
            return;
        }

        try {
            // Load available profiles
            const response = await fetch('api/wordpress-profiles.php?action=list&active_only=true');
            const result = await response.json();

            if (!result.success) {
                utils.showToast('Failed to load WordPress profiles', 'error');
                return;
            }

            const profiles = result.profiles;

            if (profiles.length === 0) {
                utils.showToast('No active WordPress profiles found. Please create a profile in Settings first.', 'warning');
                return;
            }

            // If only one profile, show title customization modal
            if (profiles.length === 1) {
                this.showTitleCustomizationModal(profiles[0]);
                return;
            }

            // Show profile selection modal
            this.showProfileSelectionModal(profiles);

        } catch (error) {
            console.error('Error loading profiles:', error);
            utils.showToast('Failed to load WordPress profiles', 'error');
        }
    }

    showProfileSelectionModal(profiles) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'wordpressProfileSelectorModal';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fab fa-wordpress me-2"></i>
                            Select WordPress Profile
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p class="text-muted">Choose which WordPress profile to post this chapter to:</p>
                        <div class="list-group">
                            ${profiles.map(profile => `
                                <button type="button" class="list-group-item list-group-item-action"
                                        onclick="chapterView.showTitleCustomizationModal(${JSON.stringify(profile).replace(/"/g, '&quot;')}); bootstrap.Modal.getInstance(document.getElementById('wordpressProfileSelectorModal')).hide();">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">${profile.profile_name}</h6>
                                        <small class="text-muted">${new URL(profile.site_url).hostname}</small>
                                    </div>
                                    <small class="text-muted">${profile.site_url}</small>
                                </button>
                            `).join('')}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Clean up when modal is hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    showTitleCustomizationModal(profile) {
        // Generate default title for preview
        const novelTitle = this.novel.translated_title || this.novel.original_title;
        let defaultTitle = `${novelTitle} - Chapter ${this.chapter.chapter_number}`;
        if (this.chapter.translated_title) {
            defaultTitle += `: ${this.chapter.translated_title}`;
        }

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'wordpressTitleCustomizationModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fab fa-wordpress me-2"></i>
                            Customize WordPress Post Title
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <p class="text-muted">
                                <strong>Profile:</strong> ${profile.profile_name} (${new URL(profile.site_url).hostname})
                            </p>
                        </div>

                        <div class="mb-3">
                            <label for="customPostTitle" class="form-label">
                                <strong>Post Title</strong>
                            </label>
                            <input type="text" class="form-control" id="customPostTitle"
                                   value="${defaultTitle}" placeholder="Enter custom title or leave default">
                            <div class="form-text">
                                Leave empty to use the default title format, or enter a custom title for this WordPress post.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Categories</label>
                            <div class="input-group">
                                <select class="form-select" id="chapterCategorySelect" multiple>
                                    <option value="">Loading categories...</option>
                                </select>
                                <button class="btn btn-outline-secondary" type="button" id="loadChapterCategories" title="Refresh categories">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                <small class="text-muted">Hold Ctrl/Cmd to select multiple categories. Leave empty to use profile default.</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label"><strong>Preview:</strong></label>
                            <div class="border rounded p-2 bg-light">
                                <small class="text-muted">Default title:</small><br>
                                <span class="text-dark">${defaultTitle}</span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-success" onclick="chapterView.postToWordPressWithTitle(${profile.id})">
                            <i class="fab fa-wordpress me-1"></i>
                            Post to WordPress
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Load categories for this profile
        this.loadCategoriesForChapterPosting(profile.id);

        // Add event listener for refresh categories button
        const loadCategoriesBtn = document.getElementById('loadChapterCategories');
        if (loadCategoriesBtn) {
            loadCategoriesBtn.addEventListener('click', () => {
                this.loadCategoriesForChapterPosting(profile.id);
            });
        }

        // Clean up when modal is hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    async postToWordPressWithTitle(profileId) {
        const customTitleInput = document.getElementById('customPostTitle');
        const customTitle = customTitleInput ? customTitleInput.value.trim() : '';

        // Get selected categories
        const categorySelect = document.getElementById('chapterCategorySelect');
        const selectedCategories = categorySelect ? Array.from(categorySelect.selectedOptions).map(option => option.value).filter(val => val) : [];

        // Hide the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('wordpressTitleCustomizationModal'));
        if (modal) {
            modal.hide();
        }

        // Post with custom title and categories
        await this.postToWordPress(profileId, customTitle, selectedCategories);
    }

    async postToWordPress(profileId = null, customTitle = null, categories = null) {
        if (!this.chapter.translated_content) {
            utils.showToast('Chapter must be translated before posting to WordPress', 'error');
            return;
        }

        const button = document.getElementById('wordpress-post-btn');
        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Posting...';
        }

        try {
            // Validate profile if specified
            if (profileId) {
                const profileResponse = await fetch(`api/wordpress-profiles.php?action=get&id=${profileId}`);
                const profileResult = await profileResponse.json();

                if (!profileResult.success) {
                    utils.showToast('WordPress profile not found', 'error');
                    return;
                }
            } else {
                // No profile specified - this shouldn't happen with the new system
                utils.showToast('No WordPress profile selected. Please select a profile to post to.', 'error');
                return;
            }

            // Check if chapter is already posted
            const statusResult = await utils.makeApiRequest(`api/wordpress.php?chapter_id=${this.chapter.id}`);
            if (statusResult.success && statusResult.status.posted) {
                const postData = statusResult.status.post_data;
                utils.showToast('Chapter already posted to WordPress', 'info');
                if (postData.wordpress_url) {
                    // Show option to view the post
                    const viewPost = confirm('Chapter already posted. Would you like to view it on WordPress?');
                    if (viewPost) {
                        window.open(postData.wordpress_url, '_blank');
                    }
                }
                return;
            }

            // Post the chapter
            const requestBody = {
                action: 'post_chapter',
                chapter_id: this.chapter.id
            };

            // Profile ID is required for the new system
            requestBody.profile_id = profileId;

            // Add custom title if provided
            if (customTitle && customTitle.length > 0) {
                requestBody.custom_title = customTitle;
            }

            // Add categories if provided
            if (categories && categories.length > 0) {
                requestBody.categories = categories;
            }

            const result = await utils.makeApiRequest('api/wordpress.php', {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });

            if (result.success) {
                utils.showToast('Chapter posted to WordPress successfully!', 'success');

                // Update button to show success and provide link
                if (button && result.wordpress_url) {
                    button.innerHTML = '<i class="fas fa-check me-1"></i>Posted';
                    button.onclick = () => window.open(result.wordpress_url, '_blank');
                    button.title = 'View on WordPress';
                    button.classList.remove('btn-outline-success');
                    button.classList.add('btn-success');
                }

                // Show success message with link
                if (result.wordpress_url) {
                    const viewPost = confirm('Chapter posted successfully! Would you like to view it on WordPress?');
                    if (viewPost) {
                        window.open(result.wordpress_url, '_blank');
                    }
                }
            } else {
                utils.showToast(result.error || 'Failed to post chapter to WordPress', 'error');
            }
        } catch (error) {
            console.error('WordPress posting error:', error);
            utils.showToast('Network error occurred while posting to WordPress', 'error');
        } finally {
            if (button && !button.classList.contains('btn-success')) {
                button.disabled = false;
                button.innerHTML = '<i class="fab fa-wordpress me-1"></i>Post to WordPress';
            }
        }
    }

    async loadCategoriesForChapterPosting(profileId) {
        const selectElement = document.getElementById('chapterCategorySelect');
        const loadButton = document.getElementById('loadChapterCategories');

        if (!selectElement) {
            console.error('Chapter category select element not found');
            return;
        }

        try {
            // Disable button and show loading state
            if (loadButton) {
                loadButton.disabled = true;
                loadButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            }

            // Clear existing options
            selectElement.innerHTML = '<option value="">Loading categories...</option>';

            const response = await fetch(`api/wordpress-categories.php?profile_id=${profileId}&_t=${Date.now()}`);
            const result = await response.json();

            if (result.success) {
                // Clear loading message
                selectElement.innerHTML = '';

                // Add categories to select
                result.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name + (category.count > 0 ? ` (${category.count})` : '');
                    selectElement.appendChild(option);
                });

                if (result.categories.length === 0) {
                    selectElement.innerHTML = '<option value="">No categories found</option>';
                }
            } else {
                selectElement.innerHTML = '<option value="">Failed to load categories</option>';
                utils.showToast(result.error || 'Failed to load categories', 'error');
            }
        } catch (error) {
            console.error('Error loading categories:', error);
            selectElement.innerHTML = '<option value="">Error loading categories</option>';
            utils.showToast('Network error occurred while loading categories', 'error');
        } finally {
            // Restore button state
            if (loadButton) {
                loadButton.disabled = false;
                loadButton.innerHTML = '<i class="fas fa-sync-alt"></i>';
            }
        }
    }

    navigateChapter(direction) {
        const newChapterNumber = this.chapterNumber + direction;
        if (newChapterNumber >= 1 && newChapterNumber <= this.novel.total_chapters) {
            // Update URL without page reload
            const newUrl = `chapter-view.php?novel_id=${this.novelId}&chapter=${newChapterNumber}`;
            window.history.pushState({}, '', newUrl);

            // Update chapter number and reload chapter data
            this.chapterNumber = newChapterNumber;
            this.loadChapter();
        }
    }

    showSplitChapterModal() {
        if (!this.chapter.original_content) {
            utils.showToast('Chapter has no content to split', 'error');
            return;
        }

        const contentLength = this.chapter.original_content.length;
        const recommendedParts = Math.ceil(contentLength / 3000); // Recommend splitting every 3000 characters

        const modalHtml = `
            <div class="modal fade" id="splitChapterModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-cut me-2"></i>
                                Split Chapter ${this.chapter.chapter_number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <p class="text-muted">
                                    This chapter has <strong>${contentLength.toLocaleString()}</strong> characters.
                                    Large chapters may cause translation timeouts.
                                </p>
                                <p class="text-info">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    Recommended: Split into <strong>${recommendedParts}</strong> parts for optimal translation.
                                </p>
                            </div>
                            <div class="mb-3">
                                <label for="splitParts" class="form-label">Number of parts to split into:</label>
                                <input type="number" class="form-control" id="splitParts"
                                       min="2" max="10" value="${recommendedParts}">
                                <div class="form-text">
                                    Each part will be approximately ${Math.round(contentLength / recommendedParts).toLocaleString()} characters.
                                </div>
                            </div>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                <strong>Note:</strong> This will split the chapter content at natural boundaries
                                (paragraphs/sentences) and create separate chunks that can be translated individually.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-success" onclick="chapterView.splitChapter()">
                                <i class="fas fa-cut me-1"></i>
                                Split Chapter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if present
        const existingModal = document.getElementById('splitChapterModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('splitChapterModal'));
        modal.show();

        // Update character count when parts number changes
        const splitPartsInput = document.getElementById('splitParts');
        splitPartsInput.addEventListener('input', () => {
            const parts = parseInt(splitPartsInput.value) || 2;
            const avgCharsPerPart = Math.round(contentLength / parts);
            const helpText = splitPartsInput.nextElementSibling;
            helpText.textContent = `Each part will be approximately ${avgCharsPerPart.toLocaleString()} characters.`;
        });
    }

    async splitChapter() {
        const splitParts = parseInt(document.getElementById('splitParts').value);

        if (!splitParts || splitParts < 2 || splitParts > 10) {
            utils.showToast('Please enter a valid number of parts (2-10)', 'error');
            return;
        }

        const modal = bootstrap.Modal.getInstance(document.getElementById('splitChapterModal'));
        modal.hide();

        utils.showLoading(true, 'Splitting chapter...');

        try {
            const response = await utils.makeApiRequest('api/chapter-split.php', {
                method: 'POST',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    chapter_number: this.chapterNumber,
                    split_parts: splitParts
                })
            });

            if (response.success) {
                utils.showToast(`Chapter successfully split into ${response.data.chunks_created} parts`, 'success');

                // Reload chapter to show updated chunk information
                await this.loadChapter();
            } else {
                utils.showToast(`Failed to split chapter: ${response.error}`, 'error');
            }
        } catch (error) {
            console.error('Split chapter error:', error);
            utils.showToast('Network error occurred while splitting chapter', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    toggleSection(button) {
        const icon = button.querySelector('.toggle-icon');
        const isExpanded = button.getAttribute('aria-expanded') === 'true';

        // Toggle icon
        if (isExpanded) {
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-right');
        } else {
            icon.classList.remove('fa-chevron-right');
            icon.classList.add('fa-chevron-down');
        }
    }

    expandAllSections() {
        const originalSection = document.getElementById('original-content-section');
        const translatedSection = document.getElementById('translated-content-section');
        const toggleButtons = document.querySelectorAll('.section-toggle');

        // Show both sections
        if (originalSection && !originalSection.classList.contains('show')) {
            new bootstrap.Collapse(originalSection, { show: true });
        }
        if (translatedSection && !translatedSection.classList.contains('show')) {
            new bootstrap.Collapse(translatedSection, { show: true });
        }

        // Update button states
        toggleButtons.forEach(button => {
            button.classList.remove('collapsed');
            button.setAttribute('aria-expanded', 'true');
            const icon = button.querySelector('.toggle-icon');
            icon.classList.remove('fa-chevron-right');
            icon.classList.add('fa-chevron-down');
        });
    }

    collapseAllSections() {
        const originalSection = document.getElementById('original-content-section');
        const translatedSection = document.getElementById('translated-content-section');
        const toggleButtons = document.querySelectorAll('.section-toggle');

        // Hide both sections
        if (originalSection && originalSection.classList.contains('show')) {
            new bootstrap.Collapse(originalSection, { hide: true });
        }
        if (translatedSection && translatedSection.classList.contains('show')) {
            new bootstrap.Collapse(translatedSection, { hide: true });
        }

        // Update button states
        toggleButtons.forEach(button => {
            button.classList.add('collapsed');
            button.setAttribute('aria-expanded', 'false');
            const icon = button.querySelector('.toggle-icon');
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-right');
        });
    }

    showOriginalOnly() {
        const originalSection = document.getElementById('original-content-section');
        const translatedSection = document.getElementById('translated-content-section');

        // Show original, hide translated
        if (originalSection && !originalSection.classList.contains('show')) {
            new bootstrap.Collapse(originalSection, { show: true });
        }
        if (translatedSection && translatedSection.classList.contains('show')) {
            new bootstrap.Collapse(translatedSection, { hide: true });
        }

        this.updateButtonStates();
    }

    showTranslatedOnly() {
        const originalSection = document.getElementById('original-content-section');
        const translatedSection = document.getElementById('translated-content-section');

        // Hide original, show translated
        if (originalSection && originalSection.classList.contains('show')) {
            new bootstrap.Collapse(originalSection, { hide: true });
        }
        if (translatedSection && !translatedSection.classList.contains('show')) {
            new bootstrap.Collapse(translatedSection, { show: true });
        }

        this.updateButtonStates();
    }

    updateButtonStates() {
        const toggleButtons = document.querySelectorAll('.section-toggle');

        toggleButtons.forEach(button => {
            const targetId = button.getAttribute('data-bs-target');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                const isExpanded = targetElement.classList.contains('show');
                const icon = button.querySelector('.toggle-icon');

                if (isExpanded) {
                    button.classList.remove('collapsed');
                    button.setAttribute('aria-expanded', 'true');
                    icon.classList.remove('fa-chevron-right');
                    icon.classList.add('fa-chevron-down');
                } else {
                    button.classList.add('collapsed');
                    button.setAttribute('aria-expanded', 'false');
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-right');
                }
            }
        });
    }

    displayError(message) {
        document.getElementById('chapter-content').innerHTML = `
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <h5>Error Loading Chapter</h5>
                <p>${message}</p>
                <div class="mt-3">
                    <button class="btn btn-primary me-2" onclick="chapterView.loadChapter()">
                        <i class="fas fa-sync-alt me-1"></i>
                        Retry
                    </button>
                    <a href="novel-details.php?id=${this.novelId}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Novel
                    </a>
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        Debug info: Novel ID: ${this.novelId}, Chapter: ${this.chapterNumber}
                    </small>
                </div>
            </div>
        `;
    }

    /**
     * Show confirmation dialog for recrawling chapter content
     */
    showRecrawlConfirmation() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-sync-alt text-warning me-2"></i>
                            Recrawl Chapter Content
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> This will replace the current chapter content with fresh content from the source website.
                        </div>
                        <p><strong>Chapter:</strong> ${this.chapter.original_title || 'Chapter ' + this.chapterNumber}</p>
                        <p><strong>Source URL:</strong> <small class="text-muted">${this.chapter.chapter_url}</small></p>
                        <p><strong>Current content length:</strong> ${(this.chapter.original_content || '').length} characters</p>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>What will happen:</h6>
                            <ul class="mb-0">
                                <li>Fresh content will be extracted from the source website</li>
                                <li>Current chapter content will be replaced</li>
                                <li>Any existing translation will be cleared</li>
                                <li>Chapter chunks will be recreated if needed</li>
                                <li>WordPress posts for this chapter will be removed</li>
                            </ul>
                        </div>

                        <p class="text-muted">This action cannot be undone. Are you sure you want to continue?</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-warning" onclick="chapterView.recrawlChapter()">
                            <i class="fas fa-sync-alt me-1"></i>
                            Recrawl Content
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // Clean up modal when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    /**
     * Show confirmation dialog for clearing chapter content
     */
    showDeleteConfirmation() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-eraser text-warning me-2"></i>
                            Clear Chapter Content
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> This will clear the chapter content while preserving the chapter entry.
                        </div>
                        <p><strong>Chapter:</strong> ${this.chapter.original_title || 'Chapter ' + this.chapterNumber}</p>
                        <p><strong>Content length:</strong> ${(this.chapter.original_content || '').length} characters</p>
                        <p><strong>Translation status:</strong> ${this.chapter.translation_status || 'pending'}</p>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>What will be cleared:</h6>
                            <ul class="mb-0">
                                <li>Chapter content (original and translated)</li>
                                <li>Chapter chunks and translation parts</li>
                                <li>Associated WordPress posts</li>
                                <li>Translation logs for this chapter</li>
                            </ul>
                        </div>

                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>What will be preserved:</h6>
                            <ul class="mb-0">
                                <li>Chapter entry in the chapter list</li>
                                <li>Chapter number and source URL</li>
                                <li>Ability to re-crawl/save content later</li>
                            </ul>
                        </div>

                        <p class="text-muted">After clearing, the chapter will appear as "not saved" and you can re-crawl it from the source URL.</p>

                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirmDelete">
                            <label class="form-check-label" for="confirmDelete">
                                I understand this will clear the chapter content
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-warning" onclick="chapterView.deleteChapter()" id="deleteConfirmBtn" disabled>
                            <i class="fas fa-eraser me-1"></i>
                            Clear Content
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // Enable delete button only when checkbox is checked
        const checkbox = modal.querySelector('#confirmDelete');
        const deleteBtn = modal.querySelector('#deleteConfirmBtn');
        checkbox.addEventListener('change', () => {
            deleteBtn.disabled = !checkbox.checked;
        });

        // Clean up modal when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    /**
     * Show edit original content modal
     */
    showEditOriginalModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'editOriginalModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-edit me-2"></i>
                            Edit Original Text - Chapter ${this.chapterNumber}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Note:</strong> This will update the original chapter content. Any existing translations will remain unchanged,
                            but you may want to re-translate the chapter if you make significant changes.
                        </div>

                        <div class="mb-3">
                            <label for="originalContentEditor" class="form-label">
                                <strong>Original Content:</strong>
                                <small class="text-muted">(${this.chapter.original_content.length} characters)</small>
                            </label>
                            <textarea class="form-control"
                                      id="originalContentEditor"
                                      rows="20"
                                      style="font-family: monospace; font-size: 14px;"
                                      placeholder="Enter the original chapter content...">${utils.escapeHtml(this.chapter.original_content)}</textarea>
                            <div class="form-text">
                                Character count: <span id="charCount">${this.chapter.original_content.length}</span>
                            </div>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirmEdit">
                            <label class="form-check-label" for="confirmEdit">
                                I understand that this will modify the original chapter content and may affect translations.
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" id="saveOriginalBtn" disabled onclick="chapterView.saveOriginalContent()">
                            <i class="fas fa-save me-1"></i>
                            Save Changes
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // Setup character counter
        const textarea = modal.querySelector('#originalContentEditor');
        const charCount = modal.querySelector('#charCount');
        textarea.addEventListener('input', () => {
            charCount.textContent = textarea.value.length;
        });

        // Enable save button only when checkbox is checked and content is not empty
        const checkbox = modal.querySelector('#confirmEdit');
        const saveBtn = modal.querySelector('#saveOriginalBtn');

        const updateSaveButton = () => {
            const hasContent = textarea.value.trim().length > 0;
            const isConfirmed = checkbox.checked;
            saveBtn.disabled = !hasContent || !isConfirmed;
        };

        checkbox.addEventListener('change', updateSaveButton);
        textarea.addEventListener('input', updateSaveButton);

        // Clean up modal when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    /**
     * Save edited original content
     */
    async saveOriginalContent() {
        const modal = document.getElementById('editOriginalModal');
        const textarea = modal.querySelector('#originalContentEditor');
        const saveBtn = modal.querySelector('#saveOriginalBtn');
        const originalText = saveBtn.innerHTML;

        const newContent = textarea.value.trim();

        if (!newContent) {
            utils.showToast('Content cannot be empty', 'error');
            return;
        }

        try {
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';

            const response = await fetch('/wc/api/chapters.php', {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'update_original_content',
                    novel_id: this.novelId,
                    chapter_number: this.chapterNumber,
                    original_content: newContent
                })
            });

            const result = await response.json();

            if (result.success) {
                utils.showToast('Original content updated successfully', 'success');

                // Close modal
                const bsModal = bootstrap.Modal.getInstance(modal);
                bsModal.hide();

                // Show summary of changes if available
                if (result.data && result.data.previous_length !== undefined) {
                    const lengthChange = result.data.new_length - result.data.previous_length;
                    const changeText = lengthChange > 0 ? `+${lengthChange}` : `${lengthChange}`;
                    utils.showToast(`Content updated (${changeText} characters)`, 'info');
                }

                // Reload the page to show updated content
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                utils.showToast(result.error || 'Failed to update original content', 'error');
            }
        } catch (error) {
            console.error('Save original content error:', error);
            utils.showToast('An error occurred while saving the content. Please try again.', 'error');
        } finally {
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalText;
        }
    }

    /**
     * Recrawl chapter content from source
     */
    async recrawlChapter() {
        // Close any open modals
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) bsModal.hide();
        });

        // Show loading state
        utils.showToast('Recrawling chapter content...', 'info');

        try {
            const response = await fetch('/wc/api/chapters.php', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'recrawl_chapter',
                    novel_id: this.novelId,
                    chapter_number: this.chapterNumber
                })
            });

            const result = await response.json();

            if (result.success) {
                utils.showToast(
                    `Chapter recrawled successfully! Content updated from ${result.data.previous_content_length} to ${result.data.new_content_length} characters.`,
                    'success'
                );

                // Reload the page to show updated content
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                utils.showToast(`Failed to recrawl chapter: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('Recrawl error:', error);
            utils.showToast('An error occurred while recrawling the chapter. Please try again.', 'error');
        }
    }

    /**
     * Clear chapter content while preserving the chapter entry
     */
    async deleteChapter() {
        // Close any open modals
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) bsModal.hide();
        });

        // Show loading state
        utils.showToast('Clearing chapter content...', 'info');

        try {
            const response = await fetch('/wc/api/chapters.php', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'delete_chapter',
                    novel_id: this.novelId,
                    chapter_number: this.chapterNumber
                })
            });

            const result = await response.json();

            if (result.success) {
                utils.showToast(
                    `Chapter content cleared successfully! Chapter ${result.data.cleared_chapter.chapter_number} can now be re-crawled.`,
                    'success'
                );

                // Reload the current page to show the cleared state
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                utils.showToast(`Failed to clear chapter content: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('Clear content error:', error);
            utils.showToast('An error occurred while clearing the chapter content. Please try again.', 'error');
        }
    }

    /**
     * Initialize Back to Top button functionality
     */
    initializeBackToTop() {
        // Remove existing back to top button if it exists
        const existingButton = document.getElementById('back-to-top-btn');
        if (existingButton) {
            existingButton.remove();
        }

        // Create back to top button
        const backToTopBtn = document.createElement('button');
        backToTopBtn.id = 'back-to-top-btn';
        backToTopBtn.className = 'btn btn-primary back-to-top-fab';
        backToTopBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
        backToTopBtn.title = 'Back to Top';
        backToTopBtn.setAttribute('aria-label', 'Scroll back to top');

        // Initially hidden
        backToTopBtn.style.display = 'none';

        // Add click handler
        backToTopBtn.addEventListener('click', () => {
            this.scrollToTop();
        });

        // Add keyboard support
        backToTopBtn.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.scrollToTop();
            }
        });

        // Add to body
        document.body.appendChild(backToTopBtn);

        // Add scroll listener
        this.handleScroll = () => {
            this.toggleBackToTopButton();
        };

        // Remove existing scroll listener if any
        window.removeEventListener('scroll', this.handleScroll);
        window.addEventListener('scroll', this.handleScroll);

        // Add cleanup on page unload
        window.addEventListener('beforeunload', () => {
            this.cleanupBackToTop();
        });
    }

    /**
     * Toggle back to top button visibility based on scroll position
     */
    toggleBackToTopButton() {
        const backToTopBtn = document.getElementById('back-to-top-btn');
        if (!backToTopBtn) return;

        const scrollThreshold = 300; // Show button after scrolling 300px
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > scrollThreshold) {
            if (backToTopBtn.style.display === 'none') {
                backToTopBtn.style.display = 'flex';
                // Trigger fade in animation
                setTimeout(() => {
                    backToTopBtn.classList.add('show');
                }, 10);
            }
        } else {
            if (backToTopBtn.style.display !== 'none') {
                backToTopBtn.classList.remove('show');
                // Hide after fade out animation completes
                setTimeout(() => {
                    if (!backToTopBtn.classList.contains('show')) {
                        backToTopBtn.style.display = 'none';
                    }
                }, 300);
            }
        }
    }

    /**
     * Smooth scroll to top of page
     */
    scrollToTop() {
        // Add visual feedback
        const backToTopBtn = document.getElementById('back-to-top-btn');
        if (backToTopBtn) {
            backToTopBtn.style.transform = 'translateY(0) scale(0.9)';
            setTimeout(() => {
                backToTopBtn.style.transform = '';
            }, 150);
        }

        // Smooth scroll to top
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    /**
     * Cleanup back to top functionality
     */
    cleanupBackToTop() {
        // Remove scroll listener
        if (this.handleScroll) {
            window.removeEventListener('scroll', this.handleScroll);
        }

        // Remove button
        const backToTopBtn = document.getElementById('back-to-top-btn');
        if (backToTopBtn) {
            backToTopBtn.remove();
        }
    }

    /**
     * Show quality check modal
     */
    async showQualityCheckModal() {
        const modalId = 'chapterQualityCheckModal';

        // Remove existing modal if any
        const existingModal = document.getElementById(modalId);
        if (existingModal) {
            existingModal.remove();
        }

        // Get available AI providers
        const providers = await this.getAvailableAIProviders();

        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="${modalId}Label">
                                <i class="fas fa-check-circle me-2"></i>
                                Translation Quality Check - Chapter ${this.chapterNumber}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="chapterQualityCheckProvider" class="form-label">AI Provider for Analysis (Optional)</label>
                                    <select class="form-select" id="chapterQualityCheckProvider">
                                        <option value="">Automated Analysis Only</option>
                                        ${providers.map(provider =>
                                            `<option value="${provider.key}">${provider.name} - ${provider.description}</option>`
                                        ).join('')}
                                    </select>
                                    <div class="form-text">
                                        Select an AI provider for additional quality analysis, or leave blank for automated checks only.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">Chapter Information</h6>
                                            <small class="text-muted">
                                                Novel: ${this.novel.translated_title || this.novel.original_title}<br>
                                                Chapter: ${this.chapterNumber}<br>
                                                Status: ${this.chapter.translation_status}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="chapterQualityCheckProgress" class="d-none">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="spinner-border spinner-border-sm me-2" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <span>Analyzing translation quality...</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                         role="progressbar" style="width: 100%"></div>
                                </div>
                            </div>

                            <div id="chapterQualityCheckResults" class="d-none">
                                <!-- Results will be populated here -->
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" id="startChapterQualityCheck"
                                    onclick="chapterView.performQualityCheck()">
                                <i class="fas fa-play me-1"></i>
                                Start Quality Check
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById(modalId));
        modal.show();
    }

    /**
     * Get available AI providers for quality check
     */
    async getAvailableAIProviders() {
        try {
            const response = await utils.makeApiRequest('api/quality-check.php?action=providers', {
                method: 'GET'
            });

            if (response.success) {
                return response.providers;
            } else {
                console.error('Failed to get AI providers:', response.error);
                return [];
            }
        } catch (error) {
            console.error('Error fetching AI providers:', error);
            return [];
        }
    }

    /**
     * Perform quality check for the current chapter
     */
    async performQualityCheck() {
        const progressDiv = document.getElementById('chapterQualityCheckProgress');
        const resultsDiv = document.getElementById('chapterQualityCheckResults');
        const startButton = document.getElementById('startChapterQualityCheck');
        const providerSelect = document.getElementById('chapterQualityCheckProvider');

        // Show progress and hide results
        progressDiv.classList.remove('d-none');
        resultsDiv.classList.add('d-none');
        startButton.disabled = true;

        try {
            const requestBody = {
                novel_id: this.novelId,
                chapter_number: this.chapterNumber
            };

            // Add AI provider if selected
            if (providerSelect.value) {
                requestBody.ai_provider = providerSelect.value;
            }

            const response = await utils.makeApiRequest('api/quality-check.php', {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });

            if (response.success) {
                this.displayQualityCheckResults(response.quality_report, resultsDiv);
                utils.showToast('Quality check completed successfully', 'success');
            } else {
                throw new Error(response.error || 'Quality check failed');
            }

        } catch (error) {
            console.error('Quality check error:', error);
            resultsDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Quality check failed: ${error.message}
                </div>
            `;
            resultsDiv.classList.remove('d-none');
            utils.showToast('Quality check failed: ' + error.message, 'error');
        } finally {
            progressDiv.classList.add('d-none');
            startButton.disabled = false;
        }
    }

    /**
     * Display quality check results (reuse methods from novel-details.js)
     */
    displayQualityCheckResults(qualityReport, resultsDiv) {
        const overallScore = qualityReport.overall_score;
        const scoreClass = this.getScoreClass(overallScore.total_score);

        let html = `
            <div class="quality-check-results">
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card border-${scoreClass}">
                            <div class="card-header bg-${scoreClass} text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    Overall Quality Score
                                </h6>
                            </div>
                            <div class="card-body text-center">
                                <h2 class="text-${scoreClass}">${overallScore.total_score}/100</h2>
                                <p class="mb-0">${overallScore.grade}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Chapter Information
                                </h6>
                            </div>
                            <div class="card-body">
                                <small class="text-muted">
                                    Original: ${qualityReport.chapter_info.original_length} chars<br>
                                    Translated: ${qualityReport.chapter_info.translated_length} chars<br>
                                    ${qualityReport.chapter_info.has_chunks ? 'Split into chunks' : 'Single chapter'}
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-clock me-2"></i>
                                    Analysis Details
                                </h6>
                            </div>
                            <div class="card-body">
                                <small class="text-muted">
                                    Execution time: ${qualityReport.execution_time}s<br>
                                    ${qualityReport.ai_analysis ? `AI Provider: ${qualityReport.ai_analysis.provider_used}` : 'Automated analysis only'}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
        `;

        // Add detailed check results
        const checks = [
            { key: 'consistency_check', title: 'Content Consistency', icon: 'fas fa-balance-scale' },
            { key: 'name_dictionary_check', title: 'Name Dictionary Application', icon: 'fas fa-book' },
            { key: 'formatting_check', title: 'Formatting Preservation', icon: 'fas fa-align-left' },
            { key: 'punctuation_check', title: 'Punctuation Consistency', icon: 'fas fa-quote-right' },
            { key: 'structure_check', title: 'Structural Integrity', icon: 'fas fa-sitemap' }
        ];

        html += '<div class="row">';

        checks.forEach(check => {
            if (qualityReport[check.key]) {
                const checkResult = qualityReport[check.key];
                const checkClass = checkResult.status === 'good' ? 'success' : 'warning';

                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card border-${checkClass}">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="${check.icon} me-2"></i>
                                    ${check.title}
                                    <span class="badge bg-${checkClass} ms-2">${checkResult.status}</span>
                                </h6>
                            </div>
                            <div class="card-body">
                                ${this.renderCheckDetails(checkResult)}
                            </div>
                        </div>
                    </div>
                `;
            }
        });

        html += '</div>';

        // Add AI analysis if available
        if (qualityReport.ai_analysis && qualityReport.ai_analysis.status === 'success') {
            html += this.renderAIAnalysis(qualityReport.ai_analysis);
        }

        html += '</div>';

        resultsDiv.innerHTML = html;
        resultsDiv.classList.remove('d-none');
    }

    /**
     * Get CSS class based on quality score
     */
    getScoreClass(score) {
        if (score >= 90) return 'success';
        if (score >= 80) return 'info';
        if (score >= 70) return 'warning';
        return 'danger';
    }

    /**
     * Render check details
     */
    renderCheckDetails(checkResult) {
        let html = '';

        if (checkResult.issues && checkResult.issues.length > 0) {
            html += '<div class="mb-2"><strong>Issues:</strong><ul class="mb-0">';
            checkResult.issues.forEach(issue => {
                html += `<li class="text-danger">${issue}</li>`;
            });
            html += '</ul></div>';
        }

        if (checkResult.warnings && checkResult.warnings.length > 0) {
            html += '<div class="mb-2"><strong>Warnings:</strong><ul class="mb-0">';
            checkResult.warnings.forEach(warning => {
                html += `<li class="text-warning">${warning}</li>`;
            });
            html += '</ul></div>';
        }

        if (checkResult.metrics) {
            html += '<div><strong>Metrics:</strong><br>';
            Object.entries(checkResult.metrics).forEach(([key, value]) => {
                const displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                html += `<small class="text-muted">${displayKey}: ${value}<br></small>`;
            });
            html += '</div>';
        }

        if (!checkResult.issues?.length && !checkResult.warnings?.length) {
            html = '<span class="text-success"><i class="fas fa-check me-1"></i>No issues found</span>';
        }

        return html;
    }

    /**
     * Render AI analysis results
     */
    renderAIAnalysis(aiAnalysis) {
        return `
            <div class="card border-primary mt-3">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-robot me-2"></i>
                        AI Quality Analysis
                        <span class="badge bg-light text-dark ms-2">Score: ${aiAnalysis.score}/10</span>
                    </h6>
                </div>
                <div class="card-body">
                    ${aiAnalysis.issues && aiAnalysis.issues.length > 0 ? `
                        <div class="mb-3">
                            <strong>AI-Identified Issues:</strong>
                            <ul class="mb-0">
                                ${aiAnalysis.issues.map(issue => `<li class="text-danger">${issue}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${aiAnalysis.recommendations && aiAnalysis.recommendations.length > 0 ? `
                        <div class="mb-3">
                            <strong>Recommendations:</strong>
                            <ul class="mb-0">
                                ${aiAnalysis.recommendations.map(rec => `<li class="text-info">${rec}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    <small class="text-muted">
                        Provider: ${aiAnalysis.provider_used} |
                        Execution time: ${aiAnalysis.execution_time}s
                    </small>
                </div>
            </div>
        `;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (typeof window.novelId !== 'undefined' && typeof window.chapterNumber !== 'undefined') {
        window.chapterView = new ChapterView(window.novelId, window.chapterNumber);
    }
});
