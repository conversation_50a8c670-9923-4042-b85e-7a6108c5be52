[2025-07-07 14:28:05] Simple Word Export Request | Context: {"novel_id":7,"chapter":56}
[2025-07-07 14:28:06] Simple Word Export Success | Context: {"novel_id":7,"chapter":56,"file_size":11871,"filename":"[I_Was_Reincarnated_as_the_Villainesss_Brother_but_]_Chapter56_Simple.docx"}
[2025-07-08 01:32:45] Chapter Chunks API: GET request received | Context: {"chapter_id":1542,"method":"GET"}
[2025-07-08 01:32:47] Chapter Chunks API: Translation request received | Context: {"chunk_id":605,"target_language":"en","raw_input":"{\"chunk_id\":605,\"target_language\":\"en\"}"}
[2025-07-08 01:33:13] Chapter Chunks API: GET request received | Context: {"chapter_id":1542,"method":"GET"}
[2025-07-08 01:34:29] Chapter Chunks API: Translation request received | Context: {"chunk_id":606,"target_language":"en","raw_input":"{\"chunk_id\":606,\"target_language\":\"en\"}"}
[2025-07-08 01:34:55] Chapter Chunks API: GET request received | Context: {"chapter_id":1542,"method":"GET"}
[2025-07-08 01:36:04] Word Export Request | Context: {"novel_id":7,"chapter":55,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-07-08 01:36:04] Word Export Success | Context: {"novel_id":7,"chapter":55,"file_size":12042,"filename":"[I_Was_Reincarnated_as_the_Villainesss_Brother_but_]_Chapter55.docx"}
[2025-07-08 01:41:57] Chapter Chunks API: GET request received | Context: {"chapter_id":1542,"method":"GET"}
[2025-07-08 01:42:00] Chapter Chunks API: GET request received | Context: {"chapter_id":1542,"method":"GET"}
[2025-07-08 01:42:02] Chapter Chunks API: Translation request received | Context: {"chunk_id":605,"target_language":"en","raw_input":"{\"chunk_id\":605,\"target_language\":\"en\"}"}
[2025-07-08 01:42:14] Chapter Chunks API: GET request received | Context: {"chapter_id":1542,"method":"GET"}
[2025-07-08 01:43:45] Chapter Chunks API: Translation request received | Context: {"chunk_id":606,"target_language":"en","raw_input":"{\"chunk_id\":606,\"target_language\":\"en\"}"}
[2025-07-08 01:44:16] Chapter Chunks API: GET request received | Context: {"chapter_id":1542,"method":"GET"}
[2025-07-08 01:47:38] Chapter Chunks API: GET request received | Context: {"chapter_id":1542,"method":"GET"}
[2025-07-08 01:47:41] Chapter Chunks API: GET request received | Context: {"chapter_id":1542,"method":"GET"}
[2025-07-08 01:48:21] Chapter Chunks API: GET request received | Context: {"chapter_id":1542,"method":"GET"}
[2025-07-08 01:48:23] Chapter Chunks API: Translation request received | Context: {"chunk_id":607,"target_language":"en","raw_input":"{\"chunk_id\":607,\"target_language\":\"en\"}"}
[2025-07-08 01:49:14] Chapter Chunks API: GET request received | Context: {"chapter_id":1542,"method":"GET"}
[2025-07-08 01:49:21] Chapter Chunks API: Translation request received | Context: {"chunk_id":608,"target_language":"en","raw_input":"{\"chunk_id\":608,\"target_language\":\"en\"}"}
[2025-07-08 01:49:48] Chapter Chunks API: GET request received | Context: {"chapter_id":1542,"method":"GET"}
[2025-07-08 02:01:19] Chapter Chunks API: GET request received | Context: {"chapter_id":1544,"method":"GET"}
[2025-07-08 02:01:21] Chapter Chunks API: Translation request received | Context: {"chunk_id":609,"target_language":"en","raw_input":"{\"chunk_id\":609,\"target_language\":\"en\"}"}
[2025-07-08 02:02:26] Chapter Chunks API: GET request received | Context: {"chapter_id":1544,"method":"GET"}
[2025-07-08 02:02:31] Chapter Chunks API: Translation request received | Context: {"chunk_id":610,"target_language":"en","raw_input":"{\"chunk_id\":610,\"target_language\":\"en\"}"}
[2025-07-08 02:02:58] Chapter Chunks API: GET request received | Context: {"chapter_id":1544,"method":"GET"}
[2025-07-08 02:03:25] Chapter Chunks API: GET request received | Context: {"chapter_id":1544,"method":"GET"}
[2025-07-08 07:11:08] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-08 07:11:16] Chapter Chunks API: Translation request received | Context: {"chunk_id":611,"target_language":"en","raw_input":"{\"chunk_id\":611,\"target_language\":\"en\"}"}
[2025-07-08 07:13:35] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-08 07:15:12] Chapter Chunks API: Translation request received | Context: {"chunk_id":612,"target_language":"en","raw_input":"{\"chunk_id\":612,\"target_language\":\"en\"}"}
[2025-07-08 07:15:24] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-08 07:19:28] Chapter Chunks API: Translation request received | Context: {"chunk_id":613,"target_language":"en","raw_input":"{\"chunk_id\":613,\"target_language\":\"en\"}"}
[2025-07-08 07:24:11] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-08 07:24:34] Chapter Chunks API: Translation request received | Context: {"chunk_id":614,"target_language":"en","raw_input":"{\"chunk_id\":614,\"target_language\":\"en\"}"}
[2025-07-08 07:26:06] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-08 07:29:08] Chapter Chunks API: GET request received | Context: {"chapter_id":172,"method":"GET"}
[2025-07-08 07:29:24] Chapter Chunks API: Translation request received | Context: {"chunk_id":615,"target_language":"en","raw_input":"{\"chunk_id\":615,\"target_language\":\"en\"}"}
[2025-07-08 09:39:13] Chapter Chunks API: GET request received | Context: {"chapter_id":172,"method":"GET"}
[2025-07-08 09:39:31] Chapter Chunks API: GET request received | Context: {"chapter_id":172,"method":"GET"}
[2025-07-08 09:39:32] Chapter Chunks API: Translation request received | Context: {"chunk_id":615,"target_language":"en","raw_input":"{\"chunk_id\":615,\"target_language\":\"en\"}"}
[2025-07-08 10:50:19] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-08 10:50:24] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-08 10:52:16] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-08 10:52:18] Chapter Chunks API: Translation request received | Context: {"chunk_id":611,"target_language":"en","raw_input":"{\"chunk_id\":611,\"target_language\":\"en\"}"}
[2025-07-08 10:58:21] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-08 11:52:05] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-08 11:52:08] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-08 11:52:21] Chapter Chunks API: Translation request received | Context: {"chunk_id":612,"target_language":"en","raw_input":"{\"chunk_id\":612,\"target_language\":\"en\"}"}
[2025-07-08 11:54:54] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-08 13:08:01] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-08 13:08:06] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-08 13:08:29] Chapter Chunks API: Translation request received | Context: {"chunk_id":611,"target_language":"en","raw_input":"{\"chunk_id\":611,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-08 13:08:29] Chapter Chunks API: Using POV preference | Context: {"chunk_id":611,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-08 13:12:20] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 05:41:45] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 05:41:48] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 05:42:04] Chapter Chunks API: Translation request received | Context: {"chunk_id":611,"target_language":"en","raw_input":"{\"chunk_id\":611,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 05:42:04] Chapter Chunks API: Using POV preference | Context: {"chunk_id":611,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 05:43:52] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 06:11:34] Chapter Chunks API: Translation request received | Context: {"chunk_id":612,"target_language":"en","raw_input":"{\"chunk_id\":612,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 06:11:34] Chapter Chunks API: Using POV preference | Context: {"chunk_id":612,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 06:12:22] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 06:17:40] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 06:17:55] Chapter Chunks API: Translation request received | Context: {"chunk_id":613,"target_language":"en","raw_input":"{\"chunk_id\":613,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 06:17:55] Chapter Chunks API: Using POV preference | Context: {"chunk_id":613,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 14:30:24] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 14:30:33] Chapter Chunks API: Translation request received | Context: {"chunk_id":614,"target_language":"en","raw_input":"{\"chunk_id\":614,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 14:30:33] Chapter Chunks API: Using POV preference | Context: {"chunk_id":614,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 14:31:28] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 14:31:55] Chapter Chunks API: GET request received | Context: {"chapter_id":172,"method":"GET"}
[2025-07-09 14:31:59] Chapter Chunks API: GET request received | Context: {"chapter_id":172,"method":"GET"}
[2025-07-09 14:32:36] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 14:32:38] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 14:32:47] Chapter Chunks API: Translation request received | Context: {"chunk_id":611,"target_language":"en","raw_input":"{\"chunk_id\":611,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 14:32:47] Chapter Chunks API: Using POV preference | Context: {"chunk_id":611,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 14:35:19] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 14:35:50] Chapter Chunks API: Translation request received | Context: {"chunk_id":612,"target_language":"en","raw_input":"{\"chunk_id\":612,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 14:35:50] Chapter Chunks API: Using POV preference | Context: {"chunk_id":612,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 14:36:06] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 14:38:27] Chapter Chunks API: Translation request received | Context: {"chunk_id":613,"target_language":"en","raw_input":"{\"chunk_id\":613,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 14:38:27] Chapter Chunks API: Using POV preference | Context: {"chunk_id":613,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 14:41:04] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 14:41:40] Chapter Chunks API: Translation request received | Context: {"chunk_id":614,"target_language":"en","raw_input":"{\"chunk_id\":614,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 14:41:40] Chapter Chunks API: Using POV preference | Context: {"chunk_id":614,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 14:42:10] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 15:00:54] Chapter Chunks API: GET request received | Context: {"chapter_id":172,"method":"GET"}
[2025-07-09 15:02:28] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 15:39:34] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 15:39:36] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 15:39:42] Chapter Chunks API: Translation request received | Context: {"chunk_id":611,"target_language":"en","raw_input":"{\"chunk_id\":611,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 15:39:42] Chapter Chunks API: Using POV preference | Context: {"chunk_id":611,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 15:42:02] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 15:43:47] Chapter Chunks API: Translation request received | Context: {"chunk_id":612,"target_language":"en","raw_input":"{\"chunk_id\":612,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 15:43:47] Chapter Chunks API: Using POV preference | Context: {"chunk_id":612,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 15:44:04] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 15:44:10] Chapter Chunks API: Translation request received | Context: {"chunk_id":613,"target_language":"en","raw_input":"{\"chunk_id\":613,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 15:44:10] Chapter Chunks API: Using POV preference | Context: {"chunk_id":613,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 15:46:38] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 15:46:45] Chapter Chunks API: Translation request received | Context: {"chunk_id":614,"target_language":"en","raw_input":"{\"chunk_id\":614,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 15:46:45] Chapter Chunks API: Using POV preference | Context: {"chunk_id":614,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 15:47:15] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 15:52:26] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 15:52:28] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 15:52:37] Chapter Chunks API: Translation request received | Context: {"chunk_id":611,"target_language":"en","raw_input":"{\"chunk_id\":611,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 15:52:37] Chapter Chunks API: Using POV preference | Context: {"chunk_id":611,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 15:54:49] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 15:54:55] Chapter Chunks API: Translation request received | Context: {"chunk_id":612,"target_language":"en","raw_input":"{\"chunk_id\":612,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 15:54:55] Chapter Chunks API: Using POV preference | Context: {"chunk_id":612,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 15:55:07] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 15:57:22] Chapter Chunks API: Translation request received | Context: {"chunk_id":613,"target_language":"en","raw_input":"{\"chunk_id\":613,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 15:57:22] Chapter Chunks API: Using POV preference | Context: {"chunk_id":613,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 16:02:01] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 16:02:07] Chapter Chunks API: Translation request received | Context: {"chunk_id":614,"target_language":"en","raw_input":"{\"chunk_id\":614,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 16:02:07] Chapter Chunks API: Using POV preference | Context: {"chunk_id":614,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 16:02:35] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 23:09:39] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 23:09:42] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 23:10:08] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 23:10:13] Chapter Chunks API: Translation request received | Context: {"chunk_id":611,"target_language":"en","raw_input":"{\"chunk_id\":611,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 23:10:13] Chapter Chunks API: Using POV preference | Context: {"chunk_id":611,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 23:10:13] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":611,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-09 23:11:51] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 23:13:02] Chapter Chunks API: Translation request received | Context: {"chunk_id":612,"target_language":"en","raw_input":"{\"chunk_id\":612,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 23:13:02] Chapter Chunks API: Using POV preference | Context: {"chunk_id":612,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 23:13:02] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":612,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-09 23:13:21] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 23:23:49] Chapter Chunks API: Translation request received | Context: {"chunk_id":613,"target_language":"en","raw_input":"{\"chunk_id\":613,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 23:23:49] Chapter Chunks API: Using POV preference | Context: {"chunk_id":613,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 23:23:49] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":613,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-09 23:25:41] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-09 23:27:29] Chapter Chunks API: Translation request received | Context: {"chunk_id":614,"target_language":"en","raw_input":"{\"chunk_id\":614,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-09 23:27:29] Chapter Chunks API: Using POV preference | Context: {"chunk_id":614,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-09 23:27:29] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":614,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-09 23:28:33] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-10 00:29:24] Chapter Chunks API: GET request received | Context: {"chapter_id":172,"method":"GET"}
[2025-07-10 00:39:22] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-10 00:39:24] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-10 00:39:40] Chapter Chunks API: Translation request received | Context: {"chunk_id":611,"target_language":"en","raw_input":"{\"chunk_id\":611,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 00:39:40] Chapter Chunks API: Using POV preference | Context: {"chunk_id":611,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 00:39:40] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":611,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 00:41:53] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-10 00:42:00] Chapter Chunks API: Translation request received | Context: {"chunk_id":612,"target_language":"en","raw_input":"{\"chunk_id\":612,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 00:42:00] Chapter Chunks API: Using POV preference | Context: {"chunk_id":612,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 00:42:00] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":612,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 00:42:52] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-10 00:43:55] Chapter Chunks API: Translation request received | Context: {"chunk_id":613,"target_language":"en","raw_input":"{\"chunk_id\":613,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 00:43:55] Chapter Chunks API: Using POV preference | Context: {"chunk_id":613,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 00:43:55] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":613,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 00:46:34] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-10 01:00:33] Chapter Chunks API: Translation request received | Context: {"chunk_id":614,"target_language":"en","raw_input":"{\"chunk_id\":614,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 01:00:33] Chapter Chunks API: Using POV preference | Context: {"chunk_id":614,"novel_id":1,"chapter_id":171,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 01:00:33] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":614,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 01:01:34] Chapter Chunks API: GET request received | Context: {"chapter_id":171,"method":"GET"}
[2025-07-10 01:14:26] Chapter Chunks API: GET request received | Context: {"chapter_id":172,"method":"GET"}
[2025-07-10 01:14:35] Chapter Chunks API: Translation request received | Context: {"chunk_id":615,"target_language":"en","raw_input":"{\"chunk_id\":615,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 01:14:35] Chapter Chunks API: Using POV preference | Context: {"chunk_id":615,"novel_id":1,"chapter_id":172,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 01:14:35] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":615,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 01:27:00] Chapter Chunks API: GET request received | Context: {"chapter_id":172,"method":"GET"}
[2025-07-10 01:32:56] Chapter Chunks API: GET request received | Context: {"chapter_id":172,"method":"GET"}
[2025-07-10 01:33:01] Chapter Chunks API: Translation request received | Context: {"chunk_id":616,"target_language":"en","raw_input":"{\"chunk_id\":616,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 01:33:01] Chapter Chunks API: Using POV preference | Context: {"chunk_id":616,"novel_id":1,"chapter_id":172,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 01:33:01] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":616,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 01:33:30] Chapter Chunks API: GET request received | Context: {"chapter_id":172,"method":"GET"}
[2025-07-10 01:35:06] Chapter Chunks API: Translation request received | Context: {"chunk_id":617,"target_language":"en","raw_input":"{\"chunk_id\":617,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 01:35:06] Chapter Chunks API: Using POV preference | Context: {"chunk_id":617,"novel_id":1,"chapter_id":172,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 01:35:06] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":617,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 01:35:38] Chapter Chunks API: GET request received | Context: {"chapter_id":172,"method":"GET"}
[2025-07-10 01:39:30] Chapter Chunks API: GET request received | Context: {"chapter_id":173,"method":"GET"}
[2025-07-10 01:40:08] Chapter Chunks API: Translation request received | Context: {"chunk_id":618,"target_language":"en","raw_input":"{\"chunk_id\":618,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 01:40:08] Chapter Chunks API: Using POV preference | Context: {"chunk_id":618,"novel_id":1,"chapter_id":173,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 01:40:08] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":618,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 01:43:38] Chapter Chunks API: GET request received | Context: {"chapter_id":173,"method":"GET"}
[2025-07-10 05:52:30] Chapter Chunks API: GET request received | Context: {"chapter_id":173,"method":"GET"}
[2025-07-10 05:52:46] Chapter Chunks API: Translation request received | Context: {"chunk_id":619,"target_language":"en","raw_input":"{\"chunk_id\":619,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 05:52:46] Chapter Chunks API: Using POV preference | Context: {"chunk_id":619,"novel_id":1,"chapter_id":173,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 05:52:46] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":619,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 05:53:09] Chapter Chunks API: GET request received | Context: {"chapter_id":173,"method":"GET"}
[2025-07-10 06:04:12] Chapter Chunks API: Translation request received | Context: {"chunk_id":620,"target_language":"en","raw_input":"{\"chunk_id\":620,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 06:04:12] Chapter Chunks API: Using POV preference | Context: {"chunk_id":620,"novel_id":1,"chapter_id":173,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 06:04:12] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":620,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 06:04:29] Chapter Chunks API: GET request received | Context: {"chapter_id":173,"method":"GET"}
[2025-07-10 06:09:28] Chapter Chunks API: GET request received | Context: {"chapter_id":173,"method":"GET"}
[2025-07-10 06:56:41] Chapter Chunks API: GET request received | Context: {"chapter_id":174,"method":"GET"}
[2025-07-10 06:56:46] Chapter Chunks API: Translation request received | Context: {"chunk_id":621,"target_language":"en","raw_input":"{\"chunk_id\":621,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 06:56:46] Chapter Chunks API: Using POV preference | Context: {"chunk_id":621,"novel_id":1,"chapter_id":174,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 06:56:46] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":621,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 06:59:18] Chapter Chunks API: GET request received | Context: {"chapter_id":174,"method":"GET"}
[2025-07-10 07:07:08] Chapter Chunks API: Translation request received | Context: {"chunk_id":622,"target_language":"en","raw_input":"{\"chunk_id\":622,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 07:07:08] Chapter Chunks API: Using POV preference | Context: {"chunk_id":622,"novel_id":1,"chapter_id":174,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 07:07:08] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":622,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 07:07:45] Chapter Chunks API: GET request received | Context: {"chapter_id":174,"method":"GET"}
[2025-07-10 07:11:54] Chapter Chunks API: Translation request received | Context: {"chunk_id":623,"target_language":"en","raw_input":"{\"chunk_id\":623,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 07:11:54] Chapter Chunks API: Using POV preference | Context: {"chunk_id":623,"novel_id":1,"chapter_id":174,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 07:11:54] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":623,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 07:36:06] Chapter Chunks API: Translation request received | Context: {"chunk_id":623,"target_language":"en","raw_input":"{\"chunk_id\":623,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 07:36:06] Chapter Chunks API: Using POV preference | Context: {"chunk_id":623,"novel_id":1,"chapter_id":174,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 07:36:06] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":623,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 07:36:27] Chapter Chunks API: GET request received | Context: {"chapter_id":174,"method":"GET"}
[2025-07-10 07:36:39] Chapter Chunks API: GET request received | Context: {"chapter_id":174,"method":"GET"}
[2025-07-10 07:36:47] Chapter Chunks API: Translation request received | Context: {"chunk_id":623,"target_language":"en","raw_input":"{\"chunk_id\":623,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 07:36:47] Chapter Chunks API: Using POV preference | Context: {"chunk_id":623,"novel_id":1,"chapter_id":174,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 07:36:47] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":623,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 07:51:02] Chapter Chunks API: GET request received | Context: {"chapter_id":174,"method":"GET"}
[2025-07-10 07:51:10] Chapter Chunks API: Translation request received | Context: {"chunk_id":623,"target_language":"en","raw_input":"{\"chunk_id\":623,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 07:51:10] Chapter Chunks API: Using POV preference | Context: {"chunk_id":623,"novel_id":1,"chapter_id":174,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 07:51:10] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":623,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 13:09:20] Chapter Chunks API: Translation request received | Context: {"chunk_id":623,"target_language":"en","raw_input":"{\"chunk_id\":623,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 13:09:20] Chapter Chunks API: Using POV preference | Context: {"chunk_id":623,"novel_id":1,"chapter_id":174,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 13:09:20] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":623,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 13:09:20] Chapter Chunks API: Resetting error chunk for retry | Context: {"chunk_id":623,"previous_status":"error"}
[2025-07-10 13:10:26] Chapter Chunks API: Translation request received | Context: {"chunk_id":623,"target_language":"en","raw_input":"{\"chunk_id\":623,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 13:10:26] Chapter Chunks API: Using POV preference | Context: {"chunk_id":623,"novel_id":1,"chapter_id":174,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 13:10:26] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":623,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 13:10:26] Chapter Chunks API: Resetting error chunk for retry | Context: {"chunk_id":623,"previous_status":"error"}
[2025-07-10 13:11:42] Chapter Chunks API: Translation request received | Context: {"chunk_id":623,"target_language":"en","raw_input":"{\"chunk_id\":623,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 13:11:42] Chapter Chunks API: Using POV preference | Context: {"chunk_id":623,"novel_id":1,"chapter_id":174,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 13:11:42] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":623,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 13:11:42] Chapter Chunks API: Resetting error chunk for retry | Context: {"chunk_id":623,"previous_status":"error"}
[2025-07-10 13:13:21] Chapter Chunks API: Translation request received | Context: {"chunk_id":623,"target_language":"en","raw_input":"{\"chunk_id\":623,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-10 13:13:21] Chapter Chunks API: Using POV preference | Context: {"chunk_id":623,"novel_id":1,"chapter_id":174,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-10 13:13:21] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":623,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-10 13:13:21] Chapter Chunks API: Resetting error chunk for retry | Context: {"chunk_id":623,"previous_status":"error"}
[2025-07-11 00:50:21] Chapter Chunks API: GET request received | Context: {"chapter_id":174,"method":"GET"}
[2025-07-11 00:53:09] Chapter Chunks API: GET request received | Context: {"chapter_id":173,"method":"GET"}
[2025-07-11 00:56:26] Chapter Chunks API: GET request received | Context: {"chapter_id":175,"method":"GET"}
[2025-07-11 00:56:38] Chapter Chunks API: Translation request received | Context: {"chunk_id":624,"target_language":"en","raw_input":"{\"chunk_id\":624,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-11 00:56:38] Chapter Chunks API: Using POV preference | Context: {"chunk_id":624,"novel_id":1,"chapter_id":175,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-11 00:56:38] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":624,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-11 00:58:16] Chapter Chunks API: GET request received | Context: {"chapter_id":175,"method":"GET"}
[2025-07-11 01:01:15] Chapter Chunks API: Translation request received | Context: {"chunk_id":625,"target_language":"en","raw_input":"{\"chunk_id\":625,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-11 01:01:15] Chapter Chunks API: Using POV preference | Context: {"chunk_id":625,"novel_id":1,"chapter_id":175,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-11 01:01:15] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":625,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-11 01:05:17] Chapter Chunks API: GET request received | Context: {"chapter_id":175,"method":"GET"}
[2025-07-11 02:02:05] Chapter Chunks API: Translation request received | Context: {"chunk_id":626,"target_language":"en","raw_input":"{\"chunk_id\":626,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-11 02:02:05] Chapter Chunks API: Using POV preference | Context: {"chunk_id":626,"novel_id":1,"chapter_id":175,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-11 02:02:05] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":626,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-11 02:06:01] Chapter Chunks API: GET request received | Context: {"chapter_id":175,"method":"GET"}
[2025-07-11 02:08:03] Chapter Chunks API: GET request received | Context: {"chapter_id":175,"method":"GET"}
[2025-07-11 02:08:14] Chapter Chunks API: GET request received | Context: {"chapter_id":176,"method":"GET"}
[2025-07-11 02:08:24] Chapter Chunks API: Translation request received | Context: {"chunk_id":627,"target_language":"en","raw_input":"{\"chunk_id\":627,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-11 02:08:24] Chapter Chunks API: Using POV preference | Context: {"chunk_id":627,"novel_id":1,"chapter_id":176,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-11 02:08:24] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":627,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-11 02:16:31] Chapter Chunks API: GET request received | Context: {"chapter_id":176,"method":"GET"}
[2025-07-11 02:16:31] Chapter Chunks API: GET request received | Context: {"chapter_id":176,"method":"GET"}
[2025-07-11 02:27:40] Chapter Chunks API: Translation request received | Context: {"chunk_id":628,"target_language":"en","raw_input":"{\"chunk_id\":628,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-11 02:27:40] Chapter Chunks API: Using POV preference | Context: {"chunk_id":628,"novel_id":1,"chapter_id":176,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-11 02:27:40] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":628,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-11 02:29:34] Chapter Chunks API: GET request received | Context: {"chapter_id":176,"method":"GET"}
[2025-07-11 02:32:42] Chapter Chunks API: Translation request received | Context: {"chunk_id":629,"target_language":"en","raw_input":"{\"chunk_id\":629,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-11 02:32:42] Chapter Chunks API: Using POV preference | Context: {"chunk_id":629,"novel_id":1,"chapter_id":176,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-11 02:32:42] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":629,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-11 02:36:42] Chapter Chunks API: GET request received | Context: {"chapter_id":176,"method":"GET"}
[2025-07-11 02:37:36] Chapter Chunks API: Translation request received | Context: {"chunk_id":630,"target_language":"en","raw_input":"{\"chunk_id\":630,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-11 02:37:36] Chapter Chunks API: Using POV preference | Context: {"chunk_id":630,"novel_id":1,"chapter_id":176,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-11 02:37:36] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":630,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-11 02:38:14] Chapter Chunks API: GET request received | Context: {"chapter_id":176,"method":"GET"}
[2025-07-11 03:32:43] Chapter Chunks API: Translation request received | Context: {"chunk_id":631,"target_language":"en","raw_input":"{\"chunk_id\":631,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-11 03:32:43] Chapter Chunks API: Using POV preference | Context: {"chunk_id":631,"novel_id":1,"chapter_id":176,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-11 03:32:43] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":631,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-11 03:33:05] Chapter Chunks API: GET request received | Context: {"chapter_id":176,"method":"GET"}
[2025-07-11 03:53:00] Chapter Chunks API: GET request received | Context: {"chapter_id":177,"method":"GET"}
[2025-07-11 03:53:05] Chapter Chunks API: Translation request received | Context: {"chunk_id":632,"target_language":"en","raw_input":"{\"chunk_id\":632,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-11 03:53:05] Chapter Chunks API: Using POV preference | Context: {"chunk_id":632,"novel_id":1,"chapter_id":177,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-11 03:53:05] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":632,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-11 03:57:01] Chapter Chunks API: GET request received | Context: {"chapter_id":177,"method":"GET"}
[2025-07-11 06:16:12] Chapter Chunks API: Translation request received | Context: {"chunk_id":633,"target_language":"en","raw_input":"{\"chunk_id\":633,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-11 06:16:12] Chapter Chunks API: Using POV preference | Context: {"chunk_id":633,"novel_id":1,"chapter_id":177,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-11 06:16:12] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":633,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-11 06:19:07] Chapter Chunks API: GET request received | Context: {"chapter_id":177,"method":"GET"}
[2025-07-11 06:28:08] Chapter Chunks API: Translation request received | Context: {"chunk_id":634,"target_language":"en","raw_input":"{\"chunk_id\":634,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-11 06:28:08] Chapter Chunks API: Using POV preference | Context: {"chunk_id":634,"novel_id":1,"chapter_id":177,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-11 06:28:08] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":634,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-11 06:29:21] Chapter Chunks API: GET request received | Context: {"chapter_id":177,"method":"GET"}
[2025-07-12 14:34:37] Chapter Chunks API: GET request received | Context: {"chapter_id":177,"method":"GET"}
[2025-07-12 14:35:14] Chapter Chunks API: GET request received | Context: {"chapter_id":177,"method":"GET"}
[2025-07-12 14:35:18] Chapter Chunks API: GET request received | Context: {"chapter_id":178,"method":"GET"}
[2025-07-12 14:35:53] Chapter Chunks API: Translation request received | Context: {"chunk_id":635,"target_language":"en","raw_input":"{\"chunk_id\":635,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-12 14:35:53] Chapter Chunks API: Using POV preference | Context: {"chunk_id":635,"novel_id":1,"chapter_id":178,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-12 14:35:53] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":635,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-12 14:38:28] Chapter Chunks API: GET request received | Context: {"chapter_id":178,"method":"GET"}
[2025-07-12 14:54:58] Chapter Chunks API: Translation request received | Context: {"chunk_id":636,"target_language":"en","raw_input":"{\"chunk_id\":636,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-12 14:54:58] Chapter Chunks API: Using POV preference | Context: {"chunk_id":636,"novel_id":1,"chapter_id":178,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-12 14:54:58] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":636,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-12 14:57:01] Chapter Chunks API: GET request received | Context: {"chapter_id":178,"method":"GET"}
[2025-07-12 15:11:36] Chapter Chunks API: Translation request received | Context: {"chunk_id":637,"target_language":"en","raw_input":"{\"chunk_id\":637,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-12 15:11:36] Chapter Chunks API: Using POV preference | Context: {"chunk_id":637,"novel_id":1,"chapter_id":178,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-12 15:11:36] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":637,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-12 15:12:22] Chapter Chunks API: GET request received | Context: {"chapter_id":178,"method":"GET"}
[2025-07-12 15:26:02] Chapter Chunks API: GET request received | Context: {"chapter_id":179,"method":"GET"}
[2025-07-12 15:26:06] Chapter Chunks API: Translation request received | Context: {"chunk_id":638,"target_language":"en","raw_input":"{\"chunk_id\":638,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-12 15:26:06] Chapter Chunks API: Using POV preference | Context: {"chunk_id":638,"novel_id":1,"chapter_id":179,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-12 15:26:06] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":638,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-12 15:28:08] Chapter Chunks API: GET request received | Context: {"chapter_id":179,"method":"GET"}
[2025-07-12 15:53:12] Chapter Chunks API: Translation request received | Context: {"chunk_id":639,"target_language":"en","raw_input":"{\"chunk_id\":639,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-12 15:53:12] Chapter Chunks API: Using POV preference | Context: {"chunk_id":639,"novel_id":1,"chapter_id":179,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-12 15:53:12] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":639,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-12 15:55:05] Chapter Chunks API: GET request received | Context: {"chapter_id":179,"method":"GET"}
[2025-07-12 15:56:11] Chapter Chunks API: Translation request received | Context: {"chunk_id":640,"target_language":"en","raw_input":"{\"chunk_id\":640,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-12 15:56:11] Chapter Chunks API: Using POV preference | Context: {"chunk_id":640,"novel_id":1,"chapter_id":179,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-12 15:56:11] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":640,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-12 15:58:09] Chapter Chunks API: GET request received | Context: {"chapter_id":179,"method":"GET"}
[2025-07-14 00:04:26] Preview API Error: Unsupported URL or invalid format | Context: {"url":"https:\/\/www.69shuba.com\/book\/44221\/","trace":"#0 C:\\xampp\\htdocs\\wc\\api\\preview.php(75): NovelManager->previewNovel('https:\/\/www.69s...')\n#1 {main}"}
[2025-07-14 00:04:50] Preview API Error: Unsupported URL or invalid format | Context: {"url":"https:\/\/www.69shuba.com\/book\/44221\/","trace":"#0 C:\\xampp\\htdocs\\wc\\api\\preview.php(75): NovelManager->previewNovel('https:\/\/www.69s...')\n#1 {main}"}
[2025-07-14 00:07:49] Preview API Error: Unsupported URL or invalid format | Context: {"url":"https:\/\/www.69shuba.com\/book\/44221.htm","trace":"#0 C:\\xampp\\htdocs\\wc\\api\\preview.php(75): NovelManager->previewNovel('https:\/\/www.69s...')\n#1 {main}"}
[2025-07-14 00:07:57] Preview API Error: Unsupported URL or invalid format | Context: {"url":"https:\/\/www.69shuba.com\/book\/44221.htm","trace":"#0 C:\\xampp\\htdocs\\wc\\api\\preview.php(75): NovelManager->previewNovel('https:\/\/www.69s...')\n#1 {main}"}
[2025-07-14 00:41:13] Chapter Chunks API: GET request received | Context: {"chapter_id":180,"method":"GET"}
[2025-07-14 00:41:21] Chapter Chunks API: Translation request received | Context: {"chunk_id":641,"target_language":"en","raw_input":"{\"chunk_id\":641,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-14 00:41:21] Chapter Chunks API: Using POV preference | Context: {"chunk_id":641,"novel_id":1,"chapter_id":180,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-14 00:41:21] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":641,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-14 00:42:42] Chapter Chunks API: GET request received | Context: {"chapter_id":180,"method":"GET"}
[2025-07-14 02:12:50] Chapter Chunks API: Translation request received | Context: {"chunk_id":642,"target_language":"en","raw_input":"{\"chunk_id\":642,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-14 02:12:50] Chapter Chunks API: Using POV preference | Context: {"chunk_id":642,"novel_id":1,"chapter_id":180,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-14 02:12:50] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":642,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-14 02:14:28] Chapter Chunks API: GET request received | Context: {"chapter_id":180,"method":"GET"}
[2025-07-15 14:11:38] Chapter Chunks API: GET request received | Context: {"chapter_id":175,"method":"GET"}
[2025-07-15 14:11:50] Chapter Chunks API: GET request received | Context: {"chapter_id":175,"method":"GET"}
[2025-07-15 14:11:53] Chapter Chunks API: GET request received | Context: {"chapter_id":175,"method":"GET"}
[2025-07-15 14:12:01] Chapter Chunks API: Translation request received | Context: {"chunk_id":624,"target_language":"en","raw_input":"{\"chunk_id\":624,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-15 14:12:01] Chapter Chunks API: Using POV preference | Context: {"chunk_id":624,"novel_id":1,"chapter_id":175,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-15 14:12:01] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":624,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-15 14:13:53] Chapter Chunks API: GET request received | Context: {"chapter_id":175,"method":"GET"}
[2025-07-15 14:13:59] Chapter Chunks API: Translation request received | Context: {"chunk_id":625,"target_language":"en","raw_input":"{\"chunk_id\":625,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-15 14:13:59] Chapter Chunks API: Using POV preference | Context: {"chunk_id":625,"novel_id":1,"chapter_id":175,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-15 14:13:59] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":625,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-15 14:15:45] Chapter Chunks API: GET request received | Context: {"chapter_id":175,"method":"GET"}
[2025-07-15 14:15:50] Chapter Chunks API: Translation request received | Context: {"chunk_id":626,"target_language":"en","raw_input":"{\"chunk_id\":626,\"target_language\":\"en\",\"pov_preference\":\"third_person_omniscient\"}"}
[2025-07-15 14:15:50] Chapter Chunks API: Using POV preference | Context: {"chunk_id":626,"novel_id":1,"chapter_id":175,"pov_preference":"third_person_omniscient","source":"request_override"}
[2025-07-15 14:15:50] Chapter Chunks API: Added narrative context with POV analysis | Context: {"chunk_id":626,"optimal_perspective":"third_person_omniscient","user_selected":true}
[2025-07-15 14:18:01] Chapter Chunks API: GET request received | Context: {"chapter_id":175,"method":"GET"}
[2025-07-15 14:41:17] Chapter Chunks API: GET request received | Context: {"chapter_id":175,"method":"GET"}
[2025-07-15 14:41:56] Quality Check API: Request received | Context: {"novel_id":1,"chapter_number":175,"ai_provider":"gemini_20","raw_input":"{\"novel_id\":1,\"chapter_number\":175,\"ai_provider\":\"gemini_20\"}"}
[2025-07-15 14:56:46] Quality Check API: Request received | Context: {"novel_id":1,"chapter_number":175,"ai_provider":"gemini_25","raw_input":"{\"novel_id\":1,\"chapter_number\":175,\"ai_provider\":\"gemini_25\"}"}
[2025-07-15 14:58:33] Quality Check API: Request received | Context: {"novel_id":1,"chapter_number":175,"ai_provider":"deepseek","raw_input":"{\"novel_id\":1,\"chapter_number\":175,\"ai_provider\":\"deepseek\"}"}
[2025-07-15 14:59:07] Quality Check API: Request received | Context: {"novel_id":1,"chapter_number":175,"ai_provider":"gemini_25","raw_input":"{\"novel_id\":1,\"chapter_number\":175,\"ai_provider\":\"gemini_25\"}"}
[2025-07-15 15:04:19] Quality Check API: Request received | Context: {"novel_id":1,"chapter_number":175,"ai_provider":"gemini_25","raw_input":"{\"novel_id\":1,\"chapter_number\":175,\"ai_provider\":\"gemini_25\"}"}
[2025-07-15 15:04:43] Quality Check API: Request received | Context: {"novel_id":1,"chapter_number":175,"ai_provider":"gemini_25","raw_input":"{\"novel_id\":1,\"chapter_number\":175,\"ai_provider\":\"gemini_25\"}"}
[2025-07-15 15:11:26] Chapter Chunks API: GET request received | Context: {"chapter_id":175,"method":"GET"}
[2025-07-15 15:11:34] Quality Check API: Request received | Context: {"novel_id":1,"chapter_number":175,"ai_provider":"gemini_25","raw_input":"{\"novel_id\":1,\"chapter_number\":175,\"ai_provider\":\"gemini_25\"}"}
[2025-07-15 15:11:58] Quality Check API: Request received | Context: {"novel_id":1,"chapter_number":175,"ai_provider":null,"raw_input":"{\"novel_id\":1,\"chapter_number\":175}"}
[2025-07-15 15:19:27] Quality Check API: Request received | Context: {"novel_id":1,"chapter_number":175,"ai_provider":"gemini_25","raw_input":"{\"novel_id\":1,\"chapter_number\":175,\"ai_provider\":\"gemini_25\"}"}
[2025-07-15 15:27:43] Quality Check API: Request received | Context: {"novel_id":1,"chapter_number":175,"ai_provider":"gemini_25","raw_input":"{\"novel_id\":1,\"chapter_number\":175,\"ai_provider\":\"gemini_25\"}"}
[2025-07-15 15:28:13] Quality Check API: Request received | Context: {"novel_id":1,"chapter_number":175,"ai_provider":null,"raw_input":"{\"novel_id\":1,\"chapter_number\":175}"}
[2025-07-15 15:28:26] Quality Check API: Request received | Context: {"novel_id":1,"chapter_number":175,"ai_provider":null,"raw_input":"{\"novel_id\":1,\"chapter_number\":175}"}
[2025-07-15 15:28:50] Quality Check API: Request received | Context: {"novel_id":1,"chapter_number":175,"ai_provider":null,"raw_input":"{\"novel_id\":1,\"chapter_number\":175}"}
