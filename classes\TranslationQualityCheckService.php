<?php
/**
 * Translation Quality Check Service
 * Analyzes translation quality without performing retranslation
 * Checks consistency, name dictionary application, formatting, and punctuation
 */

class TranslationQualityCheckService {
    private $db;
    private $nameSubstitutionService;
    private $automaticFormattingService;
    private $aiProviderManager;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->nameSubstitutionService = new NameSubstitutionService();
        $this->automaticFormattingService = new AutomaticFormattingService();
        $this->aiProviderManager = new AIProviderManager();
    }
    
    /**
     * Perform comprehensive quality check on translated content
     */
    public function performQualityCheck(int $novelId, int $chapterNumber, string $aiProvider = null): array {
        $startTime = microtime(true);
        
        try {
            // Get chapter data
            $chapterData = $this->getChapterData($novelId, $chapterNumber);
            if (!$chapterData) {
                return [
                    'success' => false,
                    'error' => 'Chapter not found or has no translation'
                ];
            }
            
            // Get name dictionary for the novel
            $nameDictionary = $this->getNameDictionary($novelId);
            
            // Perform various quality checks
            $qualityReport = [
                'chapter_info' => [
                    'novel_id' => $novelId,
                    'chapter_number' => $chapterNumber,
                    'original_length' => strlen($chapterData['original_content']),
                    'translated_length' => strlen($chapterData['translated_content']),
                    'has_chunks' => $chapterData['has_chunks']
                ],
                'consistency_check' => $this->checkConsistency($chapterData['original_content'], $chapterData['translated_content']),
                'name_dictionary_check' => $this->checkNameDictionaryApplication($chapterData['translated_content'], $nameDictionary, $chapterData['original_content']),
                'formatting_check' => $this->checkFormattingPreservation($chapterData['original_content'], $chapterData['translated_content']),
                'punctuation_check' => $this->checkPunctuationConsistency($chapterData['original_content'], $chapterData['translated_content']),
                'structure_check' => $this->checkStructuralIntegrity($chapterData['original_content'], $chapterData['translated_content'])
            ];

            // Generate actionable recommendations and corrections
            $qualityReport['recommendations'] = $this->generateActionableRecommendations($qualityReport, $chapterData, $nameDictionary);
            $qualityReport['auto_corrections'] = $this->generateAutoCorrections($qualityReport, $chapterData, $nameDictionary);
            
            // If AI provider is specified, perform AI-assisted quality analysis
            if ($aiProvider) {
                $qualityReport['ai_analysis'] = $this->performAIQualityAnalysis(
                    $chapterData['original_content'], 
                    $chapterData['translated_content'], 
                    $aiProvider,
                    $nameDictionary
                );
            }
            
            // Calculate overall quality score
            $qualityReport['overall_score'] = $this->calculateOverallQualityScore($qualityReport);
            $qualityReport['execution_time'] = round(microtime(true) - $startTime, 2);
            
            return [
                'success' => true,
                'quality_report' => $qualityReport
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Quality check failed: ' . $e->getMessage(),
                'execution_time' => round(microtime(true) - $startTime, 2)
            ];
        }
    }
    
    /**
     * Get chapter data including original and translated content
     */
    private function getChapterData(int $novelId, int $chapterNumber): ?array {
        $chapter = $this->db->fetchOne(
            "SELECT id, original_content, translated_content, translation_status 
             FROM chapters 
             WHERE novel_id = ? AND chapter_number = ?",
            [$novelId, $chapterNumber]
        );
        
        if (!$chapter || empty($chapter['translated_content'])) {
            return null;
        }
        
        // Check if chapter has chunks
        $chunkCount = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM chapter_chunks WHERE chapter_id = ?",
            [$chapter['id']]
        );
        
        $chapter['has_chunks'] = $chunkCount['count'] > 0;
        
        return $chapter;
    }
    
    /**
     * Get name dictionary for the novel
     */
    private function getNameDictionary(int $novelId): array {
        return $this->db->fetchAll(
            "SELECT original_name, romanization, translation, name_type, frequency
             FROM name_dictionary
             WHERE novel_id = ? AND original_name IS NOT NULL AND TRIM(original_name) != ''
             ORDER BY frequency DESC, name_type ASC",
            [$novelId]
        );
    }
    
    /**
     * Check consistency between original and translated text
     */
    private function checkConsistency(string $originalText, string $translatedText): array {
        $issues = [];
        $warnings = [];
        
        // Check for missing content (significant length differences)
        $originalLength = mb_strlen($originalText);
        $translatedLength = mb_strlen($translatedText);
        $lengthRatio = $translatedLength / $originalLength;
        
        if ($lengthRatio < 0.5) {
            $issues[] = "Translation is significantly shorter than original (ratio: " . round($lengthRatio, 2) . ")";
        } elseif ($lengthRatio > 2.0) {
            $warnings[] = "Translation is significantly longer than original (ratio: " . round($lengthRatio, 2) . ")";
        }
        
        // Check for untranslated Japanese characters
        if (preg_match('/[\x{3040}-\x{309F}\x{30A0}-\x{30FF}\x{4E00}-\x{9FAF}]/u', $translatedText)) {
            $issues[] = "Translation contains untranslated Japanese characters";
        }
        
        // Check for incomplete sentences
        $originalSentences = $this->countSentences($originalText);
        $translatedSentences = $this->countSentences($translatedText);
        
        if (abs($originalSentences - $translatedSentences) > max(2, $originalSentences * 0.2)) {
            $warnings[] = "Sentence count mismatch: original has {$originalSentences}, translation has {$translatedSentences}";
        }
        
        return [
            'status' => empty($issues) ? 'good' : 'issues_found',
            'issues' => $issues,
            'warnings' => $warnings,
            'metrics' => [
                'length_ratio' => $lengthRatio,
                'original_sentences' => $originalSentences,
                'translated_sentences' => $translatedSentences
            ]
        ];
    }
    
    /**
     * Count sentences in text
     */
    private function countSentences(string $text): int {
        // Count sentences by looking for sentence-ending punctuation
        return preg_match_all('/[.!?。！？]/u', $text);
    }

    /**
     * Check name dictionary application in translated text
     */
    private function checkNameDictionaryApplication(string $translatedText, array $nameDictionary, string $originalText): array {
        $issues = [];
        $warnings = [];
        $correctApplications = 0;
        $missedApplications = 0;

        foreach ($nameDictionary as $nameEntry) {
            $originalName = $nameEntry['original_name'] ?? '';
            $targetName = $this->getTargetName($nameEntry);

            if (empty($originalName) || empty($targetName)) {
                continue;
            }

            // Check if original name appears in source text
            if (mb_strpos($originalText, $originalName) !== false) {
                // Original name is in source, check if properly translated
                $targetFound = $this->findNameInText($translatedText, $targetName);

                if ($targetFound) {
                    $correctApplications++;

                    // Check if original name still appears in translation (potential issue)
                    if (mb_strpos($translatedText, $originalName) !== false) {
                        $warnings[] = "Name '{$originalName}' appears in both original and translated form";
                    }
                } else {
                    // Check if it might be translated to a similar form
                    $similarFound = $this->findSimilarNameInText($translatedText, $targetName);

                    if ($similarFound) {
                        $warnings[] = "Name '{$originalName}' should be '{$targetName}' but found similar: '{$similarFound}'";
                    } else {
                        $missedApplications++;
                        $issues[] = "Name '{$originalName}' should be translated to '{$targetName}' but was not found";
                    }
                }
            }
        }

        // Check for inconsistent name usage
        $nameConsistencyIssues = $this->checkNameConsistency($translatedText, $nameDictionary);
        $issues = array_merge($issues, $nameConsistencyIssues);

        return [
            'status' => empty($issues) ? 'good' : 'issues_found',
            'issues' => $issues,
            'warnings' => $warnings,
            'metrics' => [
                'correct_applications' => $correctApplications,
                'missed_applications' => $missedApplications,
                'total_names_checked' => count($nameDictionary)
            ]
        ];
    }

    /**
     * Get target name from name dictionary entry
     */
    private function getTargetName(array $nameEntry): string {
        // Prefer translation, fall back to romanization
        if (!empty($nameEntry['translation'])) {
            return $nameEntry['translation'];
        } elseif (!empty($nameEntry['romanization'])) {
            return $nameEntry['romanization'];
        }
        return '';
    }

    /**
     * Check for name consistency issues (significantly improved to reduce false positives)
     */
    private function checkNameConsistency(string $translatedText, array $nameDictionary): array {
        $issues = [];

        // Build a map of target names to check for variations
        $nameMap = [];
        foreach ($nameDictionary as $nameEntry) {
            $targetName = $this->getTargetName($nameEntry);
            // Only check names that are long enough and not common words
            if (!empty($targetName) && strlen($targetName) > 3 && !$this->isCommonWord(strtolower($targetName))) {
                $nameMap[$targetName] = $nameEntry['original_name'];
            }
        }

        // Check for potential name variations that might indicate inconsistency
        foreach ($nameMap as $targetName => $originalName) {
            $variations = $this->findNameVariations($translatedText, $targetName);

            // Only report if we found actual meaningful variations
            if (!empty($variations)) {
                // Additional filtering to ensure these are genuine name issues
                $genuineVariations = $this->filterGenuineNameVariations($variations, $targetName);

                // Need at least 2 different forms to indicate inconsistency
                if (count($genuineVariations) >= 2) {
                    // Limit the number of variations shown to avoid overwhelming output
                    $displayVariations = array_slice($genuineVariations, 0, 3);
                    $issues[] = "Inconsistent usage of name '{$targetName}': found variations " . implode(', ', $displayVariations);
                }
            }
        }

        return $issues;
    }

    /**
     * Filter genuine name variations from false positives
     */
    private function filterGenuineNameVariations(array $variations, string $targetName): array {
        $genuine = [];
        $baseName = strtolower($targetName);

        foreach ($variations as $variation) {
            $variationLower = strtolower($variation);

            // Skip if it's a common word
            if ($this->isCommonWord($variationLower)) {
                continue;
            }

            // Skip if it's too different (likely not a real variation)
            $similarity = $this->calculateStringSimilarity($variationLower, $baseName);
            if ($similarity < 0.6) {
                continue;
            }

            // Skip if it's a completely different word pattern
            if (!$this->hasReasonableNamePattern($variation, $targetName)) {
                continue;
            }

            $genuine[] = $variation;
        }

        return $genuine;
    }

    /**
     * Check if variation has reasonable name pattern compared to original
     */
    private function hasReasonableNamePattern(string $variation, string $targetName): bool {
        $varLower = strtolower($variation);
        $targetLower = strtolower($targetName);

        // Check if they share common character patterns
        $sharedChars = 0;
        $targetLen = strlen($targetLower);

        for ($i = 0; $i < $targetLen; $i++) {
            if (strpos($varLower, $targetLower[$i]) !== false) {
                $sharedChars++;
            }
        }

        // Should share at least 60% of characters
        return ($sharedChars / $targetLen) >= 0.6;
    }

    /**
     * Find variations of a name in text (significantly improved to reduce false positives)
     */
    private function findNameVariations(string $text, string $name): array {
        $variations = [];
        $baseName = strtolower($name);
        $nameLength = strlen($baseName);

        // Skip very short names or common words to reduce false positives
        if ($nameLength <= 2 || $this->isCommonWord($baseName)) {
            return []; // Return empty array for short names or common words
        }

        // Split text into words and check for similar names
        $words = preg_split('/\s+/', $text);
        foreach ($words as $word) {
            $cleanWord = preg_replace('/[^\p{L}\p{N}]/u', '', $word);
            $cleanWordLower = strtolower($cleanWord);

            // Skip if word is too short, too different in length, or is a common word
            if (strlen($cleanWord) < 3 ||
                abs(strlen($cleanWord) - $nameLength) > 2 || // More restrictive length difference
                $this->isCommonWord($cleanWordLower)) {
                continue;
            }

            // Skip if the word is exactly the same as the name (not a variation)
            if ($cleanWordLower === $baseName) {
                if (!in_array($cleanWord, $variations)) {
                    $variations[] = $cleanWord;
                }
                continue;
            }

            // Use much more strict similarity check for actual variations
            if ($this->isActualNameVariation($cleanWordLower, $baseName, $cleanWord, $name)) {
                if (!in_array($cleanWord, $variations)) {
                    $variations[] = $cleanWord;
                }
            }
        }

        // Only report if we have the original name plus actual variations
        // Filter out single instances that might be false positives
        $meaningfulVariations = $this->filterMeaningfulVariations($variations, $name);

        // Return all meaningful variations found (including the original if found)
        return $meaningfulVariations;
    }

    /**
     * Check if a word is actually a variation of a name (not just similar)
     */
    private function isActualNameVariation(string $wordLower, string $baseName, string $originalWord, string $originalName): bool {
        // Calculate similarity
        $similarity = $this->calculateStringSimilarity($wordLower, $baseName);

        // Require very high similarity for short names
        if (strlen($baseName) <= 4 && $similarity < 0.85) {
            return false;
        }

        // Require high similarity for longer names
        if (strlen($baseName) > 4 && $similarity < 0.75) {
            return false;
        }

        // Check for common name variation patterns
        if ($this->isCommonNameVariationPattern($wordLower, $baseName)) {
            return true;
        }

        // Check if it's likely a plural/possessive form
        if ($this->isPluralOrPossessiveForm($wordLower, $baseName)) {
            return true;
        }

        // Check if it's a case variation
        if ($this->isCaseVariation($originalWord, $originalName)) {
            return true;
        }

        // For very similar words, check if they could be typos
        if ($similarity >= 0.9 && $this->couldBeTypo($wordLower, $baseName)) {
            return true;
        }

        return false;
    }

    /**
     * Check for common name variation patterns
     */
    private function isCommonNameVariationPattern(string $word, string $name): bool {
        // Check for common suffixes that indicate name variations
        $commonSuffixes = ['s', 'es', "'s", 'san', 'chan', 'kun', 'sama'];

        foreach ($commonSuffixes as $suffix) {
            if ($word === $name . $suffix || $name === $word . $suffix) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if word is plural or possessive form of name
     */
    private function isPluralOrPossessiveForm(string $word, string $name): bool {
        // Check for plural forms
        if ($word === $name . 's' || $word === $name . 'es') {
            return true;
        }

        // Check for possessive forms (without apostrophe due to text processing)
        if ($word === $name . 's' && strlen($word) === strlen($name) + 1) {
            return true;
        }

        return false;
    }

    /**
     * Check if it's just a case variation
     */
    private function isCaseVariation(string $word, string $name): bool {
        return strtolower($word) === strtolower($name) && $word !== $name;
    }

    /**
     * Check if word could be a typo of the name
     */
    private function couldBeTypo(string $word, string $name): bool {
        // Only consider as typo if very close and reasonable length
        if (abs(strlen($word) - strlen($name)) > 1) {
            return false;
        }

        // Use Levenshtein distance for typo detection
        $distance = levenshtein($word, $name);
        return $distance <= 1; // Only 1 character difference
    }

    /**
     * Filter out meaningless variations and keep only meaningful ones
     */
    private function filterMeaningfulVariations(array $variations, string $originalName): array {
        if (empty($variations)) {
            return [];
        }

        $meaningful = [];
        $baseName = strtolower($originalName);

        foreach ($variations as $variation) {
            $variationLower = strtolower($variation);

            // Always include exact case variations
            if ($variationLower === $baseName && $variation !== $originalName) {
                $meaningful[] = $variation;
                continue;
            }

            // Include the original name itself
            if ($variation === $originalName) {
                $meaningful[] = $variation;
                continue;
            }

            // Include clear name variations (plurals, possessives, etc.)
            if ($this->isCommonNameVariationPattern($variationLower, $baseName)) {
                $meaningful[] = $variation;
                continue;
            }

            // Include very close matches that are likely typos or spelling variations
            if ($this->couldBeTypo($variationLower, $baseName)) {
                $meaningful[] = $variation;
                continue;
            }

            // Include spelling variations (more permissive for longer names)
            $similarity = $this->calculateStringSimilarity($variationLower, $baseName);
            if (strlen($baseName) > 5 && $similarity > 0.7) {
                $meaningful[] = $variation;
                continue;
            }
            if (strlen($baseName) <= 5 && $similarity > 0.8) {
                $meaningful[] = $variation;
                continue;
            }
        }

        return $meaningful;
    }

    /**
     * Check if a word is a common English word (to reduce false positives)
     */
    private function isCommonWord(string $word): bool {
        $commonWords = [
            // Pronouns
            'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them',
            'my', 'your', 'his', 'her', 'its', 'our', 'their', 'mine', 'yours', 'hers', 'ours', 'theirs',
            'this', 'that', 'these', 'those',

            // Articles and prepositions
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
            'between', 'among', 'under', 'over', 'out', 'off', 'down', 'near', 'far',

            // Common verbs
            'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
            'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'shall',
            'get', 'got', 'go', 'went', 'come', 'came', 'see', 'saw', 'know', 'knew', 'think', 'thought',
            'take', 'took', 'give', 'gave', 'make', 'made', 'say', 'said', 'tell', 'told', 'ask', 'asked',
            'work', 'worked', 'try', 'tried', 'use', 'used', 'find', 'found', 'look', 'looked',
            'want', 'wanted', 'need', 'needed', 'feel', 'felt', 'seem', 'seemed', 'leave', 'left',
            'put', 'run', 'ran', 'move', 'moved', 'live', 'lived', 'believe', 'believed',

            // Common adjectives
            'good', 'bad', 'big', 'small', 'long', 'short', 'high', 'low', 'hot', 'cold', 'warm', 'cool',
            'new', 'old', 'young', 'right', 'wrong', 'true', 'false', 'real', 'fake', 'sure', 'clear',
            'hard', 'soft', 'easy', 'difficult', 'simple', 'complex', 'light', 'dark', 'bright',
            'fast', 'slow', 'quick', 'early', 'late', 'open', 'closed', 'full', 'empty', 'free', 'busy',

            // Common nouns
            'time', 'day', 'night', 'week', 'month', 'year', 'hour', 'minute', 'second',
            'man', 'woman', 'boy', 'girl', 'person', 'people', 'child', 'children',
            'way', 'place', 'thing', 'part', 'case', 'point', 'end', 'side', 'hand', 'eye', 'face',
            'head', 'body', 'heart', 'mind', 'life', 'death', 'world', 'country', 'city', 'home',
            'house', 'room', 'door', 'window', 'car', 'road', 'water', 'fire', 'air', 'food',
            'money', 'job', 'work', 'school', 'book', 'word', 'name', 'number', 'line', 'area',

            // Common adverbs
            'not', 'no', 'yes', 'now', 'then', 'here', 'there', 'where', 'when', 'how', 'why',
            'very', 'too', 'so', 'just', 'only', 'also', 'still', 'even', 'well', 'back', 'again',
            'more', 'most', 'much', 'many', 'some', 'any', 'all', 'each', 'every', 'other', 'another',

            // Numbers
            'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten',
            'first', 'second', 'third', 'last', 'next',

            // Gaming/fantasy common terms that might be confused with names
            'level', 'skill', 'magic', 'spell', 'item', 'weapon', 'armor', 'shield', 'sword', 'bow',
            'staff', 'potion', 'heal', 'attack', 'defense', 'power', 'strength', 'speed', 'luck',
            'experience', 'gold', 'silver', 'bronze', 'iron', 'steel', 'mithril', 'dragon', 'demon',
            'angel', 'god', 'goddess', 'king', 'queen', 'prince', 'princess', 'knight', 'warrior',
            'mage', 'priest', 'thief', 'archer', 'guild', 'party', 'team', 'group', 'member',
            'dungeon', 'tower', 'castle', 'village', 'town', 'forest', 'mountain', 'river', 'sea',
            'north', 'south', 'east', 'west',

            // Additional common words that cause false positives
            'hero', 'heros', 'heroes', 'zero', 'zeros', 'really', 'fireball', 'fire', 'ball',
            'acted', 'action', 'they', 'theyd', 'theyre', 'their', 'there', 'then', 'them',
            'what', 'when', 'where', 'which', 'while', 'with', 'without', 'would', 'could',
            'should', 'might', 'right', 'left', 'front', 'back', 'side', 'top', 'bottom',
            'inside', 'outside', 'around', 'through', 'across', 'along', 'against', 'toward',
            'towards', 'between', 'among', 'within', 'without', 'behind', 'beside', 'below',
            'above', 'under', 'over', 'upon', 'onto', 'into', 'unto', 'until', 'unless',
            'since', 'because', 'although', 'though', 'however', 'therefore', 'moreover',
            'furthermore', 'nevertheless', 'nonetheless', 'meanwhile', 'otherwise', 'instead',

            // Common contractions and informal words
            'dont', 'cant', 'wont', 'isnt', 'arent', 'wasnt', 'werent', 'hasnt', 'havent',
            'hadnt', 'didnt', 'doesnt', 'shouldnt', 'couldnt', 'wouldnt', 'mustnt',
            'im', 'youre', 'hes', 'shes', 'its', 'were', 'theyre', 'ive', 'youve',
            'weve', 'theyve', 'ill', 'youll', 'hell', 'shell', 'well', 'theyll',
            'id', 'youd', 'hed', 'shed', 'wed', 'theyd',

            // Common past tense and participle forms
            'walked', 'talked', 'looked', 'worked', 'played', 'stayed', 'moved', 'lived',
            'loved', 'liked', 'helped', 'asked', 'answered', 'called', 'named', 'placed',
            'faced', 'based', 'used', 'caused', 'closed', 'opened', 'started', 'ended',
            'turned', 'learned', 'earned', 'burned', 'returned', 'concerned', 'determined'
        ];

        return in_array(strtolower($word), $commonWords);
    }

    /**
     * Calculate string similarity (more accurate than levenshtein for this purpose)
     */
    private function calculateStringSimilarity(string $str1, string $str2): float {
        // Use similar_text for better similarity calculation
        $similarity = 0;
        similar_text($str1, $str2, $similarity);
        return $similarity / 100;
    }

    /**
     * Find name in text with word boundary consideration
     */
    private function findNameInText(string $text, string $name): bool {
        // Try exact match first
        if (mb_strpos($text, $name) !== false) {
            return true;
        }

        // Try case-insensitive match
        if (mb_stripos($text, $name) !== false) {
            return true;
        }

        // Try with word boundaries (for English names)
        if (preg_match('/\b' . preg_quote($name, '/') . '\b/i', $text)) {
            return true;
        }

        return false;
    }

    /**
     * Find similar name in text (for detecting close matches)
     */
    private function findSimilarNameInText(string $text, string $targetName): ?string {
        $words = preg_split('/\s+/', $text);
        $bestMatch = null;
        $bestSimilarity = 0;

        foreach ($words as $word) {
            $cleanWord = preg_replace('/[^\p{L}\p{N}]/u', '', $word);

            // Skip if too short or too different in length
            if (strlen($cleanWord) < 3 || abs(strlen($cleanWord) - strlen($targetName)) > 3) {
                continue;
            }

            $similarity = $this->calculateStringSimilarity(strtolower($cleanWord), strtolower($targetName));

            // Consider it a match if similarity is high (but not exact)
            if ($similarity > 0.8 && $similarity < 1.0 && $similarity > $bestSimilarity) {
                $bestMatch = $cleanWord;
                $bestSimilarity = $similarity;
            }
        }

        return $bestMatch;
    }

    /**
     * Generate actionable recommendations based on quality check results
     */
    private function generateActionableRecommendations(array $qualityReport, array $chapterData, array $nameDictionary): array {
        $recommendations = [];

        // Name dictionary recommendations
        if (!empty($qualityReport['name_dictionary_check']['issues'])) {
            $recommendations[] = [
                'type' => 'name_dictionary',
                'priority' => 'high',
                'title' => 'Fix Name Dictionary Issues',
                'description' => 'Apply missing name translations from dictionary',
                'action' => 'auto_fix_names',
                'actionable' => true,
                'estimated_fixes' => count($qualityReport['name_dictionary_check']['issues'])
            ];
        }

        // Formatting recommendations
        if (!empty($qualityReport['formatting_check']['issues'])) {
            $recommendations[] = [
                'type' => 'formatting',
                'priority' => 'medium',
                'title' => 'Fix Formatting Issues',
                'description' => 'Restore proper line breaks and paragraph structure',
                'action' => 'auto_fix_formatting',
                'actionable' => true,
                'estimated_fixes' => count($qualityReport['formatting_check']['issues'])
            ];
        }

        // Punctuation recommendations
        if (!empty($qualityReport['punctuation_check']['issues'])) {
            $recommendations[] = [
                'type' => 'punctuation',
                'priority' => 'medium',
                'title' => 'Fix Punctuation Issues',
                'description' => 'Correct unmatched quotes and punctuation marks',
                'action' => 'auto_fix_punctuation',
                'actionable' => true,
                'estimated_fixes' => count($qualityReport['punctuation_check']['issues'])
            ];
        }

        // Consistency recommendations
        if (!empty($qualityReport['consistency_check']['issues'])) {
            $recommendations[] = [
                'type' => 'consistency',
                'priority' => 'high',
                'title' => 'Review Translation Consistency',
                'description' => 'Consider retranslating sections with significant issues',
                'action' => 'suggest_retranslation',
                'actionable' => true,
                'estimated_fixes' => 1
            ];
        }

        // AI-based recommendations
        if (isset($qualityReport['ai_analysis']['recommendations'])) {
            foreach ($qualityReport['ai_analysis']['recommendations'] as $aiRec) {
                $recommendations[] = [
                    'type' => 'ai_suggestion',
                    'priority' => 'medium',
                    'title' => 'AI Recommendation',
                    'description' => $aiRec,
                    'action' => 'manual_review',
                    'actionable' => false,
                    'estimated_fixes' => 0
                ];
            }
        }

        return $recommendations;
    }

    /**
     * Generate automatic corrections that can be applied
     */
    private function generateAutoCorrections(array $qualityReport, array $chapterData, array $nameDictionary): array {
        $corrections = [];

        // Name dictionary corrections
        $nameCorrections = $this->generateNameCorrections($chapterData['translated_content'], $nameDictionary, $chapterData['original_content']);
        if (!empty($nameCorrections)) {
            $corrections['name_dictionary'] = $nameCorrections;
        }

        // Formatting corrections
        $formattingCorrections = $this->generateFormattingCorrections($chapterData['original_content'], $chapterData['translated_content']);
        if (!empty($formattingCorrections)) {
            $corrections['formatting'] = $formattingCorrections;
        }

        // Punctuation corrections
        $punctuationCorrections = $this->generatePunctuationCorrections($chapterData['translated_content']);
        if (!empty($punctuationCorrections)) {
            $corrections['punctuation'] = $punctuationCorrections;
        }

        return $corrections;
    }

    /**
     * Generate name dictionary corrections
     */
    private function generateNameCorrections(string $translatedText, array $nameDictionary, string $originalText): array {
        $corrections = [];

        foreach ($nameDictionary as $nameEntry) {
            $originalName = $nameEntry['original_name'] ?? '';
            $targetName = $this->getTargetName($nameEntry);

            if (empty($originalName) || empty($targetName)) {
                continue;
            }

            // Check if original name appears in source and target name is missing in translation
            if (mb_strpos($originalText, $originalName) !== false && !$this->findNameInText($translatedText, $targetName)) {
                // Look for similar names that could be corrected
                $similarName = $this->findSimilarNameInText($translatedText, $targetName);

                if ($similarName) {
                    $corrections[] = [
                        'type' => 'name_replacement',
                        'original_name' => $originalName,
                        'from' => $similarName,
                        'to' => $targetName,
                        'confidence' => 'high',
                        'description' => "Replace '{$similarName}' with '{$targetName}' (from dictionary)"
                    ];
                } else {
                    // Check if original Japanese name still exists in translation
                    if (mb_strpos($translatedText, $originalName) !== false) {
                        $corrections[] = [
                            'type' => 'name_translation',
                            'original_name' => $originalName,
                            'from' => $originalName,
                            'to' => $targetName,
                            'confidence' => 'high',
                            'description' => "Translate '{$originalName}' to '{$targetName}'"
                        ];
                    }
                }
            }
        }

        return $corrections;
    }

    /**
     * Generate formatting corrections
     */
    private function generateFormattingCorrections(string $originalText, string $translatedText): array {
        $corrections = [];

        // Check line break preservation
        $originalLineBreaks = substr_count($originalText, "\n");
        $translatedLineBreaks = substr_count($translatedText, "\n");

        if ($originalLineBreaks > $translatedLineBreaks + 2) {
            $corrections[] = [
                'type' => 'line_breaks',
                'description' => 'Restore missing line breaks to match original structure',
                'action' => 'restore_line_breaks',
                'confidence' => 'medium'
            ];
        }

        // Check paragraph structure
        $originalParagraphs = count(array_filter(explode("\n\n", $originalText), 'trim'));
        $translatedParagraphs = count(array_filter(explode("\n\n", $translatedText), 'trim'));

        if ($originalParagraphs > $translatedParagraphs + 1) {
            $corrections[] = [
                'type' => 'paragraph_structure',
                'description' => 'Restore paragraph breaks to match original structure',
                'action' => 'restore_paragraphs',
                'confidence' => 'medium'
            ];
        }

        return $corrections;
    }

    /**
     * Generate punctuation corrections
     */
    private function generatePunctuationCorrections(string $translatedText): array {
        $corrections = [];

        // Check for Japanese punctuation in English text
        if ($this->hasJapanesePunctuationIssues($translatedText)) {
            $corrections[] = [
                'type' => 'japanese_punctuation',
                'description' => 'Fix Japanese punctuation marks in English text',
                'action' => 'fix_japanese_punctuation',
                'confidence' => 'high'
            ];
        }

        // Check for unmatched quotes
        $doubleQuotes = substr_count($translatedText, '"');
        if ($doubleQuotes % 2 !== 0) {
            $corrections[] = [
                'type' => 'unmatched_quotes',
                'description' => 'Fix unmatched quotation marks',
                'action' => 'fix_quotes',
                'confidence' => 'high'
            ];
        }

        // Check for unmatched parentheses
        $openParen = substr_count($translatedText, '(');
        $closeParen = substr_count($translatedText, ')');
        if ($openParen !== $closeParen) {
            $corrections[] = [
                'type' => 'unmatched_parentheses',
                'description' => 'Fix unmatched parentheses',
                'action' => 'fix_parentheses',
                'confidence' => 'high'
            ];
        }

        return $corrections;
    }

    /**
     * Check if text has Japanese punctuation issues that can be automatically fixed
     */
    private function hasJapanesePunctuationIssues(string $text): bool {
        // Only return true if there are fixable Japanese punctuation issues
        // This determines whether to offer the "Fix Japanese Punctuation" button

        // Check for any Japanese quotes (these can be auto-fixed)
        if (preg_match('/[「」『』]/u', $text)) {
            return true;
        }

        // Check for Japanese punctuation marks (these can be auto-fixed)
        if (preg_match('/[。！？、…（）]/u', $text)) {
            return true;
        }

        // Check for Japanese brackets (these can be auto-fixed)
        if (preg_match('/[［］]/u', $text)) {
            return true;
        }

        return false;
    }

    /**
     * Find Japanese punctuation issues in translated text (only genuine issues that need manual review)
     */
    private function findJapanesePunctuationIssues(string $text): array {
        $issues = [];

        // Only report issues that cannot be automatically fixed or need manual review

        // Check for complex mixed punctuation that might need manual review
        // Look for cases where Japanese and English punctuation are mixed in ways that auto-fix might not handle correctly
        if (preg_match_all('/[「『][^」』]{50,}[」』]/u', $text, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                // Only report if it's a long passage that might need careful manual review
                if (strlen($match[0]) > 100) {
                    $preview = mb_substr($match[0], 0, 50) . '...';
                    $issues[] = "Long passage with Japanese quotes may need manual review: '{$preview}'";
                }
            }
        }

        // Check for nested or complex quote structures that auto-fix might not handle well
        if (preg_match_all('/[「『][^」』]*[「『][^」』]*[」』][^」』]*[」』]/u', $text, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $preview = mb_substr($match[0], 0, 30) . '...';
                $issues[] = "Complex nested quotes may need manual review: '{$preview}'";
            }
        }

        // Check for unusual punctuation combinations that might indicate translation errors
        if (preg_match_all('/[。！？、]{2,}/u', $text, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $issues[] = "Multiple Japanese punctuation marks: '{$match[0]}' - may indicate translation error";
            }
        }

        // Check for Japanese punctuation in the middle of English words (likely translation errors)
        if (preg_match_all('/[a-zA-Z][。！？、][a-zA-Z]/u', $text, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $issues[] = "Japanese punctuation within English word: '{$match[0]}' - likely translation error";
            }
        }

        // Check for untranslated Japanese text mixed with punctuation issues
        if (preg_match_all('/[\x{3040}-\x{309F}\x{30A0}-\x{30FF}\x{4E00}-\x{9FAF}][「」『』。！？、]/u', $text, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $issues[] = "Untranslated Japanese text with punctuation: '{$match[0]}' - needs translation";
            }
        }

        return $issues;
    }

    /**
     * Check formatting preservation
     */
    private function checkFormattingPreservation(string $originalText, string $translatedText): array {
        $issues = [];
        $warnings = [];

        // Use the existing AutomaticFormattingService for analysis
        $formattingResult = $this->automaticFormattingService->validateAndEnforceFormatting(
            $originalText,
            $translatedText,
            ['quality_check' => true]
        );

        if (!$formattingResult['formatting_preserved']) {
            $issues[] = "Formatting structure not preserved in translation";

            // Add specific formatting issues
            if (isset($formattingResult['original_validation']['issues'])) {
                foreach ($formattingResult['original_validation']['issues'] as $issue) {
                    $warnings[] = "Formatting issue: " . $issue;
                }
            }
        }

        // Check line break preservation
        $originalLineBreaks = substr_count($originalText, "\n");
        $translatedLineBreaks = substr_count($translatedText, "\n");

        if (abs($originalLineBreaks - $translatedLineBreaks) > max(2, $originalLineBreaks * 0.3)) {
            $warnings[] = "Line break count mismatch: original has {$originalLineBreaks}, translation has {$translatedLineBreaks}";
        }

        // Check paragraph structure
        $originalParagraphs = count(array_filter(explode("\n\n", $originalText), 'trim'));
        $translatedParagraphs = count(array_filter(explode("\n\n", $translatedText), 'trim'));

        if (abs($originalParagraphs - $translatedParagraphs) > max(1, $originalParagraphs * 0.2)) {
            $warnings[] = "Paragraph count mismatch: original has {$originalParagraphs}, translation has {$translatedParagraphs}";
        }

        return [
            'status' => empty($issues) ? 'good' : 'issues_found',
            'issues' => $issues,
            'warnings' => $warnings,
            'metrics' => [
                'original_line_breaks' => $originalLineBreaks,
                'translated_line_breaks' => $translatedLineBreaks,
                'original_paragraphs' => $originalParagraphs,
                'translated_paragraphs' => $translatedParagraphs,
                'formatting_preserved' => $formattingResult['formatting_preserved']
            ]
        ];
    }

    /**
     * Check punctuation consistency
     */
    private function checkPunctuationConsistency(string $originalText, string $translatedText): array {
        $issues = [];
        $warnings = [];

        // Check for dialogue punctuation consistency
        $originalDialogue = $this->countDialogueMarkers($originalText);
        $translatedDialogue = $this->countDialogueMarkers($translatedText);

        if (abs($originalDialogue - $translatedDialogue) > 0) {
            $warnings[] = "Dialogue marker count mismatch: original has {$originalDialogue}, translation has {$translatedDialogue}";
        }

        // Check for quotation marks consistency
        $originalQuotes = $this->countQuotationMarks($originalText);
        $translatedQuotes = $this->countQuotationMarks($translatedText);

        if (abs($originalQuotes['total'] - $translatedQuotes['total']) > 2) {
            $warnings[] = "Quotation mark count mismatch: original has {$originalQuotes['total']}, translation has {$translatedQuotes['total']}";
        }

        // Check for Japanese punctuation issues that need manual review (not auto-fixable)
        $manualReviewIssues = $this->findJapanesePunctuationIssues($translatedText);
        if (!empty($manualReviewIssues)) {
            foreach ($manualReviewIssues as $issue) {
                $issues[] = $issue; // These are already formatted with context
            }
        }

        // Check for unmatched punctuation
        $unmatchedPunctuation = $this->findUnmatchedPunctuation($translatedText);
        if (!empty($unmatchedPunctuation)) {
            foreach ($unmatchedPunctuation as $issue) {
                $issues[] = "Unmatched punctuation: " . $issue;
            }
        }

        // Add info about auto-fixable punctuation issues
        if ($this->hasJapanesePunctuationIssues($translatedText)) {
            $warnings[] = "Japanese punctuation marks detected - can be automatically fixed";
        }

        return [
            'status' => empty($issues) ? 'good' : 'issues_found',
            'issues' => $issues,
            'warnings' => $warnings,
            'metrics' => [
                'original_dialogue_markers' => $originalDialogue,
                'translated_dialogue_markers' => $translatedDialogue,
                'original_quotes' => $originalQuotes,
                'translated_quotes' => $translatedQuotes,
                'has_auto_fixable_punctuation' => $this->hasJapanesePunctuationIssues($translatedText)
            ]
        ];
    }

    /**
     * Count dialogue markers in text
     */
    private function countDialogueMarkers(string $text): int {
        // Count Japanese dialogue markers and English equivalents
        return preg_match_all('/[「」『』""]/', $text);
    }

    /**
     * Count quotation marks in text
     */
    private function countQuotationMarks(string $text): array {
        $single = preg_match_all("/'/", $text);
        $double = preg_match_all('/"/', $text);
        $japanese = preg_match_all('/[「」『』]/', $text);

        return [
            'single' => $single,
            'double' => $double,
            'japanese' => $japanese,
            'total' => $single + $double + $japanese
        ];
    }

    /**
     * Find unmatched punctuation
     */
    private function findUnmatchedPunctuation(string $text): array {
        $issues = [];

        // Check for unmatched parentheses
        $openParen = substr_count($text, '(');
        $closeParen = substr_count($text, ')');
        if ($openParen !== $closeParen) {
            $issues[] = "Unmatched parentheses: {$openParen} open, {$closeParen} close";
        }

        // Check for unmatched brackets
        $openBracket = substr_count($text, '[');
        $closeBracket = substr_count($text, ']');
        if ($openBracket !== $closeBracket) {
            $issues[] = "Unmatched brackets: {$openBracket} open, {$closeBracket} close";
        }

        // Check for unmatched quotes
        $doubleQuotes = substr_count($text, '"');
        if ($doubleQuotes % 2 !== 0) {
            $issues[] = "Unmatched double quotes: {$doubleQuotes} total";
        }

        return $issues;
    }

    /**
     * Check structural integrity
     */
    private function checkStructuralIntegrity(string $originalText, string $translatedText): array {
        $issues = [];
        $warnings = [];

        // Check for missing or extra sections
        $originalSections = $this->identifyTextSections($originalText);
        $translatedSections = $this->identifyTextSections($translatedText);

        if (count($originalSections) !== count($translatedSections)) {
            $warnings[] = "Section count mismatch: original has " . count($originalSections) . ", translation has " . count($translatedSections);
        }

        // Check for proper dialogue attribution
        $dialogueIssues = $this->checkDialogueAttribution($originalText, $translatedText);
        $issues = array_merge($issues, $dialogueIssues);

        // Check for narrative consistency
        $narrativeIssues = $this->checkNarrativeConsistency($translatedText);
        $warnings = array_merge($warnings, $narrativeIssues);

        return [
            'status' => empty($issues) ? 'good' : 'issues_found',
            'issues' => $issues,
            'warnings' => $warnings,
            'metrics' => [
                'original_sections' => count($originalSections),
                'translated_sections' => count($translatedSections)
            ]
        ];
    }

    /**
     * Identify text sections (paragraphs, dialogue blocks, etc.)
     */
    private function identifyTextSections(string $text): array {
        // Split by double newlines to identify major sections
        $sections = array_filter(explode("\n\n", $text), function($section) {
            return trim($section) !== '';
        });

        return array_values($sections);
    }

    /**
     * Check dialogue attribution consistency
     */
    private function checkDialogueAttribution(string $originalText, string $translatedText): array {
        $issues = [];

        // This is a simplified check - in a real implementation, you might want more sophisticated analysis
        $originalDialogueLines = preg_match_all('/^[「"]/m', $originalText);
        $translatedDialogueLines = preg_match_all('/^[""]/m', $translatedText);

        if (abs($originalDialogueLines - $translatedDialogueLines) > 2) {
            $issues[] = "Dialogue line count significantly different: original {$originalDialogueLines}, translated {$translatedDialogueLines}";
        }

        return $issues;
    }

    /**
     * Check narrative consistency
     */
    private function checkNarrativeConsistency(string $translatedText): array {
        $warnings = [];

        // Check for POV consistency (simplified check)
        $firstPerson = preg_match_all('/\b(I|me|my|mine)\b/i', $translatedText);
        $thirdPerson = preg_match_all('/\b(he|she|him|her|his|hers)\b/i', $translatedText);

        if ($firstPerson > 0 && $thirdPerson > $firstPerson * 2) {
            $warnings[] = "Mixed POV detected: both first person ({$firstPerson}) and third person ({$thirdPerson}) pronouns found";
        }

        return $warnings;
    }

    /**
     * Perform AI-assisted quality analysis
     */
    private function performAIQualityAnalysis(string $originalText, string $translatedText, string $aiProvider, array $nameDictionary): array {
        try {
            // Build AI analysis prompt
            $prompt = $this->buildQualityAnalysisPrompt($originalText, $translatedText, $nameDictionary);

            // Get AI service based on provider
            $aiService = $this->getAIServiceForProvider($aiProvider);

            // Perform AI analysis
            $result = $aiService->translateText($prompt, 'en', 'auto', [
                'type' => 'quality_analysis',
                'max_tokens' => 2048
            ]);

            if ($result['success']) {
                $analysis = $this->parseAIQualityResponse($result['translated_text']);
                $analysis['provider_used'] = $aiProvider;
                $analysis['execution_time'] = $result['execution_time'] ?? 0;
                return $analysis;
            } else {
                return [
                    'status' => 'error',
                    'error' => $result['error'] ?? 'AI analysis failed',
                    'provider_used' => $aiProvider
                ];
            }

        } catch (Exception $e) {
            return [
                'status' => 'error',
                'error' => 'AI analysis error: ' . $e->getMessage(),
                'provider_used' => $aiProvider
            ];
        }
    }

    /**
     * Build AI quality analysis prompt
     */
    private function buildQualityAnalysisPrompt(string $originalText, string $translatedText, array $nameDictionary): string {
        $nameList = '';
        if (!empty($nameDictionary)) {
            $nameList = "\n\nName Dictionary:\n";
            foreach ($nameDictionary as $name) {
                $target = $this->getTargetName($name);
                if (!empty($target)) {
                    $nameList .= "- {$name['original_name']} → {$target}\n";
                }
            }
        }

        return "Please analyze the quality of this Japanese to English translation. Focus on:\n\n" .
               "1. Accuracy and completeness\n" .
               "2. Name consistency (check against provided dictionary)\n" .
               "3. Formatting and structure preservation\n" .
               "4. Natural English flow\n" .
               "5. Cultural context preservation\n\n" .
               "Original Japanese:\n{$originalText}\n\n" .
               "English Translation:\n{$translatedText}" .
               $nameList . "\n\n" .
               "Provide a structured analysis with:\n" .
               "- Overall quality score (1-10)\n" .
               "- Specific issues found\n" .
               "- Recommendations for improvement\n" .
               "- Name dictionary compliance check\n\n" .
               "Format your response as JSON with keys: score, issues, recommendations, name_compliance";
    }

    /**
     * Get AI service for specific provider
     */
    private function getAIServiceForProvider(string $provider): object {
        // Set the provider temporarily for this analysis
        $originalProvider = $this->aiProviderManager->getActiveProvider();
        $this->aiProviderManager->setActiveProvider($provider);

        $service = $this->aiProviderManager->getTranslationService($provider);

        // Restore original provider
        $this->aiProviderManager->setActiveProvider($originalProvider);

        return $service;
    }

    /**
     * Parse AI quality response
     */
    private function parseAIQualityResponse(string $response): array {
        try {
            // Try to extract JSON from response
            $jsonMatch = [];
            if (preg_match('/\{.*\}/s', $response, $jsonMatch)) {
                $parsed = json_decode($jsonMatch[0], true);
                if ($parsed) {
                    return [
                        'status' => 'success',
                        'score' => $parsed['score'] ?? 0,
                        'issues' => $parsed['issues'] ?? [],
                        'recommendations' => $parsed['recommendations'] ?? [],
                        'name_compliance' => $parsed['name_compliance'] ?? 'unknown',
                        'raw_response' => $response
                    ];
                }
            }

            // Fallback: parse as plain text
            return [
                'status' => 'success',
                'score' => $this->extractScoreFromText($response),
                'issues' => $this->extractIssuesFromText($response),
                'recommendations' => $this->extractRecommendationsFromText($response),
                'name_compliance' => 'unknown',
                'raw_response' => $response
            ];

        } catch (Exception $e) {
            return [
                'status' => 'parse_error',
                'error' => 'Failed to parse AI response: ' . $e->getMessage(),
                'raw_response' => $response
            ];
        }
    }

    /**
     * Extract score from text response
     */
    private function extractScoreFromText(string $text): int {
        if (preg_match('/score[:\s]*(\d+)/i', $text, $matches)) {
            return min(10, max(1, (int)$matches[1]));
        }
        return 5; // Default neutral score
    }

    /**
     * Extract issues from text response
     */
    private function extractIssuesFromText(string $text): array {
        $issues = [];
        $lines = explode("\n", $text);

        foreach ($lines as $line) {
            $line = trim($line);
            if (preg_match('/^[-*•]\s*(.+)/', $line, $matches) &&
                (stripos($line, 'issue') !== false || stripos($line, 'problem') !== false)) {
                $issues[] = $matches[1];
            }
        }

        return $issues;
    }

    /**
     * Extract recommendations from text response
     */
    private function extractRecommendationsFromText(string $text): array {
        $recommendations = [];
        $lines = explode("\n", $text);

        foreach ($lines as $line) {
            $line = trim($line);
            if (preg_match('/^[-*•]\s*(.+)/', $line, $matches) &&
                (stripos($line, 'recommend') !== false || stripos($line, 'suggest') !== false)) {
                $recommendations[] = $matches[1];
            }
        }

        return $recommendations;
    }

    /**
     * Calculate overall quality score
     */
    private function calculateOverallQualityScore(array $qualityReport): array {
        $scores = [];
        $weights = [
            'consistency_check' => 0.25,
            'name_dictionary_check' => 0.20,
            'formatting_check' => 0.20,
            'punctuation_check' => 0.15,
            'structure_check' => 0.20
        ];

        foreach ($weights as $check => $weight) {
            if (isset($qualityReport[$check])) {
                $status = $qualityReport[$check]['status'];
                $issueCount = count($qualityReport[$check]['issues'] ?? []);
                $warningCount = count($qualityReport[$check]['warnings'] ?? []);

                // Calculate score based on issues (0-100 scale)
                $checkScore = 100;
                $checkScore -= $issueCount * 20; // Major penalty for issues
                $checkScore -= $warningCount * 5; // Minor penalty for warnings
                $checkScore = max(0, min(100, $checkScore));

                $scores[$check] = $checkScore * $weight;
            }
        }

        $totalScore = array_sum($scores);

        // Include AI analysis score if available
        if (isset($qualityReport['ai_analysis']['score'])) {
            $aiScore = $qualityReport['ai_analysis']['score'] * 10; // Convert to 100 scale
            $totalScore = ($totalScore * 0.8) + ($aiScore * 0.2); // 80% automated, 20% AI
        }

        return [
            'total_score' => round($totalScore, 1),
            'grade' => $this->getQualityGrade($totalScore),
            'component_scores' => $scores,
            'ai_score_included' => isset($qualityReport['ai_analysis']['score'])
        ];
    }

    /**
     * Get quality grade based on score
     */
    private function getQualityGrade(float $score): string {
        if ($score >= 90) return 'Excellent';
        if ($score >= 80) return 'Good';
        if ($score >= 70) return 'Fair';
        if ($score >= 60) return 'Poor';
        return 'Very Poor';
    }
}
?>
